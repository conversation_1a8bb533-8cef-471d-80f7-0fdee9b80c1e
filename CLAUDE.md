# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

OVV (Online Voucher Validation) Viewer is a Java Spring Boot web application with an AngularJS frontend, designed for Tesco's voucher management system. The application handles voucher validation, transaction tracking, reporting, and user management across multiple countries.

## Key Commands

### Backend (Java/Maven)
```bash
# Build WAR file
mvn clean package

# Run tests
mvn test

# Build with dependency security check
mvn clean package -Pdependency-check

# Generate JAXB classes from XSD schema
mvn jaxb:generate
```

### Frontend (AngularJS/Gulp)
```bash
cd SingApp/angular/

# Install dependencies
npm install
bower install

# Development server (with live reload)
gulp serve

# Build for production
gulp build

# Run tests
gulp test

# JavaScript linting (JSHint)
gulp scripts

# Watch mode (auto-rebuild on changes)
gulp watch

# Run single test file
karma start --single-run --browsers PhantomJS --files src/app/path/to/test.spec.js
```

### Database Setup
```bash
# Local PostgreSQL via Docker
cd tools/postgre-in-docker-compose
docker-compose up

# Database runs on port 5492
# Connection: ******************************************
# Username: ovv / Password: pass
```

## Architecture Overview

### Backend Architecture
- **Framework**: Spring 6.1.12 (Boot, Security, Data JPA)
- **Java**: 17
- **Database**: PostgreSQL with Hibernate 6.6.0
- **Build**: Maven, produces WAR for Tomcat 10.1.28
- **Key Patterns**:
  - Repository pattern with custom implementations for complex queries
  - Service layer for business logic
  - DTO pattern for API responses
  - QueryDSL for type-safe queries
  - Scheduled jobs for SFTP imports

### Frontend Architecture
- **Framework**: AngularJS 1.x with Sing Dashboard template
- **Build**: Gulp-based pipeline with Bower
- **Structure**:
  - Module-based organization (users, transactions, reports, etc.)
  - Shared components and services
  - Resource services for API communication
  - Internationalization support (CS, HU, PL, SK)

### Security Architecture
- **Authentication**: OIDC-based SSO with Tesco's identity provider
- **Authorization**: Role-based (ADMIN, USER, OFFICE_USER) with country-specific access
- **CSRF Protection**: Cookie-based with custom filter
- **Session**: Spring Security with MDC logging

## Important Conventions

### API Endpoints
- REST API under `/api/*`
- DTOs for all API responses
- Pagination using Spring Data's `Pageable`
- Error handling via `@ControllerAdvice`

### Database Migrations
- SQL scripts in `src/main/sql/updates/`
- Naming: `YYYYMMDD-NN-description.sql`
- Manual execution required

### Report Generation
- Excel reports using Apache POI
- Async processing with thread pool
- Templates in `src/main/resources/xls/`
- Large reports handled via batch processing

### Frontend Development
- Components in `SingApp/angular/src/app/modules/`
- Services use Angular's `$resource`
- Validation using Angular directives
- Tables use DataTables with server-side processing

### Testing Approach
- Backend: JUnit 5 + Mockito
- Frontend: Karma + Jasmine + PhantomJS
- Integration tests use H2 in-memory database
- No E2E tests currently active

## Key Business Logic

### Voucher Processing
- Vouchers identified by serial number and issuer
- States: ISSUED, REDEEMED, EXPIRED, CANCELLED
- Transaction tracking with store and cash register info
- Batch processing for large imports

### User Management
- Multi-role system with country-based filtering
- Office users can manage stores in their countries
- Admins have full access
- Password changes require old password validation

### Report Types
1. **Used Vouchers Report**: Transaction details within date range
2. **Category Report**: Voucher usage by product categories
3. Both support Excel export with row limits

### SFTP Integration
- Scheduled import of cash register definitions
- Files processed from `/work` to `/archive`
- CSV format with batch commit
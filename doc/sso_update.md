# OVV Viewer - Single Sign-on
- nove se veskery user management presouva do Tesco AD - pri pristupu do OVV Viewer aplikace je uzivatel automaticky presmerovan na Tesco AD login - po uspesne loginu je 
presmerovan zpatky na OVV Viewer, kde dostane sessionu - stejne jako ji dostal do ted pres OVV Viewer login formular
## Nastaveni SSO
- v OVV Viewer app.properties se musi nove nastavit nasledujici properties:
  - `sso.oauth.issuer`
    - URL OIDC serveru - z emailove komunikace se pro Tesco jedna o adresu https://login.ourtesco.com/oidc/2
    - OVV Viewer si vsechny potrebne informace o autorizacnim serveru vytahne automaticky za pomoci .well-known endpointu - 
https://login.ourtesco.com/oidc/2/.well-known/openid-configuration
  - `sso.oauth.clientId`
    - ID prirazene OVV Viewer aplikaci jakozto klientovi v OAuth flow
    - Vygenerovane na strane autorizacniho serveru (OneLogin) specialne pro OVV Viewer
  - `sso.oauth.clientSecret`
    - stejne jako clientId, akorat se jedna o klientsky secret, kterym se aplikace OVV Viewer pri loginu prokazuje, ze opravdu jde o ni
    - Take vygenerovane na strane autorizacniho serveru (OneLogin) specialne pro OVV Viewer
  - `sso.oauth.scope`
    - seznam OIDC scope, o ktere si aplikace OVV Viewer pri loginu zazada
    - Na zaklade pozadovanych scope se pak aplikaci OVV Viewer vraci seznam claimu
    - Pro spravnou funkcnost SSO v OVV Vieweru je potreba, aby byly definovane scope:
      1. zakladni OIDC claimy, na kterych stoji funkcnost samotneho protokolu - sub, aud, iat apod. - k tomu slouzi scope `openid`
      2. specificke claimy pro Tesco, na kterych stoji funkcnost OVV Viewer - preferred_username, params, groups
         - struktura claimu, ze kterych se vytvari OVV Viewer uzivatel (pozor ze musi sedet i mala/velka pismena): ```json {
  "preferred_username": "<EMAIL>",
  "groups": [
    "GG-CZ-TescoGlobal-OVV-Viewer"
  ],
  "params": {
    "EmployeeNumber": "IN12345678",
    "businessCategory": "Store",
    "division": "12345"
  }
}
``` - na strane autorizacniho serveru (OneLogin)
  - vygenerovat clientId a clientSecret pro OVV Viever - viz predchozi kroky
  - vyplnit redirect URL, na kterou se klientsky prohlizec presmeruje pri uspesnem zalogovani
    - ta je ve formatu `<ovv-viewer-url>/login/oauth2/code/default` - pri spravnem nastaveni by SSO melo zacit fungovat - veskere chyby pri SSO loginu jsou zalogovane do 
serveroveho logu a melo by z nich byt lehce poznat, kde je problem - je zapotrebi, aby autorizacni server bezel pri startu OVV Vieweru - ten si pri startu taha jeho potrebne data
## Zmeny proti user managementu primo v OVV Viewer
- v tabulce se report se pro auditni ucely ukladal pres cizi klic konkretni radek s uzivatelem, ktery jej vytvoril
  - nove se uklada pouze varchar, ktery se naplni hodnotou, ktera prisla v claimu `preferrem_username`
  - pro tohle je potreba pred spustenim nove verze OVV Viewer provest nasledujici DB upgrade, ktery zmeni typ sloupce `created_by_user_id` z ciziho klice na varchar a naplni jej 
emailem uzivatele, ktery tam byl uveden: ```sql alter table report drop constraint fk_report_created_by_user_id; alter table report alter column created_by_user_id type 
varchar(128); update report set created_by_user_id = ovv_user.email from ovv_user where ovv_user.id = report.created_by_user_id::bigint ``` - v GUI aplikace se v hornim pravem rohu 
zobrazoval username aktualne prihlaseneho uzivatele
  - nove se zde ukazuje hodnota, ktera prisla v claimu `preferred_username` - v ramci operace `Manual Redemption` se vola do externiho, ktery je v proprtach definovan pomoci 
`remoteHost.request.url`, defaultne `http://localhost:12000/receiveGateway/ovv`. V ramci tohoto volani se do requestu vklada field `operator`, ktery se doposud bral z uzivatelova 
username.
  - nove se zde pouziva hodnota claimu `EmployeeNumber` - kazda akce, ktere je vykonana uzivatelem se do ted zapisovala do server logu se zminkou o jeho username
  - nove se do server logu propisuje claim `EmployeeNumber` - v OVV Viewer user managementu mohl mit uzivatel doposud k sobe prirazenych nekolik obchodu
  - nove z AD chodi pouze jeden obchod
  - Store uzivatel ma pristup pouze ke svemu obchodu
  - Office uzivatel ma pristup ke vsem obchodum z jeho zeme
  - Admin ma jako doposud pristup ke vsem obchodum v systemu - v OVV Viewer user managementu mohl mit uzivatel doposud prirazenou pouze jednu zemi (krome admina, ktery nemel 
zadnou)
  - nove se zeme vytahne z uzivatelovi groupy
  - adminovi se stale zadna zeme neprirazuje
  - pokud se pro obycejneho uzivatela zjisti vice zemi (je clenem vice skupin skrze vicero zemi), tak se login nepovede

<h1 class="page-title" translate>redemption.heading</h1>

<vm-messages messages="messages"></vm-messages>

<section class="widget vm-filter">
    <div class="widget-body">
        <form class="form-horizontal" name="form" novalidate>
            <div class="row">
                <div class="col-md-6">
                    <fieldset>
                        <div class="form-group">
                            <label for="transactionType" class="col-sm-4 control-label" translate>redemption.transactionType</label>
                            <div class="col-sm-7">
                                <ui-select id="transactionType" name="transactionType" class="form-control"
                                       ng-model="voucher.transactionType" search-enabled="false" required ng-disabled="!(account.roles.includes('ADMIN'))">
                                    <ui-select-match allow-clear="false">
                                        {{$select.selected.title | translate}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="item in transactionTypes">
                                        {{item.title | translate}}
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="voucherNumber" class="col-sm-4 control-label" translate>redemption.voucherNumber</label>
                            <div class="col-sm-7">
                                <input type="text" id="voucherNumber" name="voucherNumber" class="form-control"
                                       ng-model="voucher.voucherNumber" ng-minlength="3" ng-maxlength="255"
                                       ng-pattern="'[a-zA-Z0-9]{3,255}'" required autofocus>
                                <vm-input-errors field="form.voucherNumber" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="store" class="col-sm-4 control-label" translate>redemption.store</label>
                            <div class="col-sm-7">
                                <ui-select id="store" name="store" class="form-control" ng-model="voucher.selectedStore" search-enabled="true" required>
                                    <ui-select-match>
                                        {{$select.selected.siteCode + ' ' + $select.selected.name}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="store in stores track by $index"
                                                       refresh="refreshStores($select.search)"
                                                       refresh-delay="2">
                                        {{store.siteCode + ' ' + store.name}}
                                    </ui-select-choices>
                                </ui-select>
                                <vm-input-errors field="form.store" required />
                            </div>
                        </div>
                         <div class="form-group">
                            <label for="device" class="col-sm-4 control-label" translate>redemption.device</label>
                            <div class="col-sm-7">
                                <input type="number" id="device" name="device" class="form-control"
                                       ng-model="voucher.selectedCashRegister" min="1" max="200" required>
                                <vm-input-errors field="form.device" required invalid />
                            </div>
                        </div>
                        <div class="form-group" ng-if="voucher.transactionType.value !== 'REVERSAL'">
                            <label for="redemptionDateTime" class="col-sm-4 control-label" translate>redemption.redemptionDateTime</label>
                            <div class="col-sm-7">
                                <input type="text" datetimepicker datetimepicker-options="{{datetimepickerOptions}}" ngDisabled="voucher.transactionType.value === 'VALIDATION'"
                                       id="redemptionDateTime" name="redemptionDateTime"
                                       class="form-control" ng-model="voucher.redemptionDateTime" />
                            </div>
                        </div>
                        <div class="form-group" ng-if="voucher.transactionType.value === 'REVERSAL'">
                            <label for="stan" class="col-sm-4 control-label" translate>redemption.stan</label>
                            <div class="col-sm-7">
                                <input type="number" id="stan" name="stan" class="form-control" ngDisabled="voucher.transactionType.value === 'REVERSAL'"
                                       ng-model="voucher.stan" required>
                                <vm-input-errors field="form.stan" required />
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>
            <div class="form-actions btn-toolbar text-align-center">
                <button type="submit" class="btn btn-primary" ng-disabled="!form.$valid || sending" ng-click="submit(form)" translate>form.send</button>
            </div>
        </form>
    </div>
</section>

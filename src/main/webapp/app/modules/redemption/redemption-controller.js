(function() {
    'use strict';

    angular.module('ovv.redemption')
        .controller('RedemptionController', RedemptionController);

    /* @ngInject */
    function RedemptionController($scope, $rootScope, $filter, StoreResource, RedemptionResource, ovvEvents, $modal) {
        var minusDays = function(date, daysCount) {
            return new Date(date.setDate(date.getDate() - daysCount));
        };

        $scope.voucher = {
            transactionType: { value: 'CHECK', title: 'transaction.type.CHECK' },
            device: 'OVV-VIEWER',
            redemptionDateTime: moment(minusDays(new Date(), 1)).format('D.M.YYYY')
        };
        $scope.transactionTypes = [
            { value: 'CHECK', title: 'transaction.type.CHECK' },
            { value: 'REVERSAL', title: 'transaction.type.REVERSAL' }
        ];
        $scope.sending = false;
        $scope.account = $rootScope.account;

        $scope.datetimepickerOptions = {
            // defaultDate: moment().subtract(1, 'days'),
            format: 'D.M.YYYY'
        };

        //focus on Voucher Number
        angular.element('#voucherNumber').focus();

        /**
         * Handler of form submit button.
         */
        $scope.submit = function() {
            console.log($scope.voucher);
            $rootScope.$broadcast(ovvEvents.CLEAR_MSGS);
            var req = {
                store: $scope.voucher.selectedStore,
                transactionType: $scope.voucher.transactionType.value,
                voucherNumber: $scope.voucher.voucherNumber,
                redemptionDate: $scope.voucher.redemptionDateTime,
                device: $scope.voucher.selectedCashRegister,
                stan: $scope.voucher.stan
            };
            $scope.sending = true;
            $scope.busy = RedemptionResource.send({}, req, function (res) {
                $scope.sending = false;
                var message = $filter('translate')('redemption.validation.' + res.resultCode) + ' ' + $filter('translate')('redemption.voucherNumber') + ' :  ' + req.voucherNumber + '. (' + $filter('translate')('transaction.type') + ': ' + $filter('translate')('transaction.type.' + req.transactionType) + ')';
                if(req.transactionType == 'CHECK' && (res.resultCode == 'AMOUNT_DEF_NOT_FOUND' || res.resultCode == 'APPROVED')){
                    // unable to resolve amount on tps server, amount needs to be set manually
                    $modal.open({
                        templateUrl: 'app/modules/redemption/redemption-set-amount.html',
                        controller: function($scope, $rootScope, $modalInstance) {
                            $scope.req = req;
                            $scope.req.transactionType = 'VALIDATION';
                            if (typeof res.amount !== 'undefined' && res.amount != null) {
                                $scope.req.amountConfirmOnly = true;
                                var fractions = res.amount % 100 != 0;
                                var amount = res.amount / 100;
                                if (fractions) {
                                    $scope.req.amount = amount.toFixed(2).replace(".", ",");
                                } else {
                                    $scope.req.amount = amount.toFixed(0);
                                }
                            }
                            $scope.close = function () {
                                $modalInstance.close();
                                clearVoucher();
                                sending(false);
                            };
                            $scope.submit = function(){
                                // Deep clone object
                                var request =JSON.parse(JSON.stringify($scope.req))
                                request.amount = parseFloat(request.amount.replace(',','.')) * 100;//send cents
                                RedemptionResource.send({}, request, function (res) {
                                    $modalInstance.close();
                                    var message = $filter('translate')('redemption.validation.' + res.resultCode) + ' '+ $filter('translate')('redemption.voucherNumber') + ' :  ' + request.voucherNumber + '. (' + $filter('translate')('transaction.type') + ': ' + $filter('translate')('transaction.type.' + request.transactionType)+ ')';
                                    $rootScope.$broadcast(getColorOfResultCode(res.resultCode), message);
                                    clearVoucher();
                                    sending(false);
                                },function (error) {
                                    $modalInstance.close();
                                    $rootScope.$broadcast(ovvEvents.ERROR_MSG, error.data);
                                    clearVoucher();
                                    sending(false);
                                });
                            };
                          },
                        size: 'lg',
                        backdrop: 'static', // resolve problem with clicking outside of modal

                    });
                }
                else {
                    $rootScope.$broadcast(getColorOfResultCode(res.resultCode), message);
                    clearVoucher();
                }
            }, function (error) {
                sending(false);
                $rootScope.$broadcast(ovvEvents.ERROR_MSG, error.data);
                clearVoucher();
            });
            $rootScope.$broadcast('busy', $scope.busy);
        };

        $scope.refreshStores = function (store) {
            if (!store) {
                store = '';
            }

            return StoreResource.find({text: store}).$promise.then(function (res) {
                $scope.stores = res;
            });
        };

        function sending(status) {
            $scope.sending = false;
        }


        function clearVoucher() {
            $scope.voucher.voucherNumber = ' ';
            $scope.form.$setPristine();
            angular.element('#voucherNumber').focus();
        }

        function getColorOfResultCode(resultCode) {
            switch (resultCode) {
                case 'APPROVED':
                case 'WRONG_VOUCHER_STATE':
                    return ovvEvents.SUCCESS_MSG;
                    break;
                case 'MESSAGE_FORMAT_ERROR':
                case 'ISSUER_NOT_FOUND':
                case 'TIMEOUT':
                case 'WRONG_STORE':
                case 'CANNOT_PROCESS':
                case 'ALREADY_USED_SAME_TRN':
                    return ovvEvents.WARNING_MSG;
                    break;
                default:
                    return ovvEvents.ERROR_MSG;
            }
        }
    }
})();

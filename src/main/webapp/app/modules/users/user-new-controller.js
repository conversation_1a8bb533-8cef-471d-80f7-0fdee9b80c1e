(function() {
    'use strict';

    angular.module('ovv.users')
        .controller('UserNewController', UserNewController);

    /* @ngInject */
    function UserNewController($scope, $rootScope, $state, $filter, UserResource, ovvEvents, validationErrors, StoreResource) {
        $scope.roles = [
            { value: 'ADMIN', title: 'user.role.ADMIN' },
            { value: 'VIEWER', title: 'user.role.VIEWER' },
            { value: 'BATCH', title: 'user.role.BATCH' },
            { value: 'MANUAL_REDEMPTION', title: 'user.role.MANUAL_REDEMPTION' },
            { value: 'STORE_MANAGER', title: 'user.role.STORE_MANAGER' }
        ];

        $scope.countries = [
            { value: 'CZ', title: 'country.CZ' },
            { value: 'PL', title: 'country.PL' },
            { value: 'SK', title: 'country.SK' },
            { value: 'HU', title: 'country.HU' }
        ];

        $scope.account = $rootScope.account;

        $scope.roleFilter = function(element) {
            if ($scope.account.roles.includes('STORE_MANAGER')) {
                return (!(element.value === 'BATCH') && !(element.value === 'VIEWER') && !(element.value === 'MANUAL_REDEMPTION')) ? false : true;
            }
            else {
                return true;
            }
        };

        $scope.user = new UserResource({ active: true });
        $scope.user.stores = [];

        //store manager can manage only users in his country
        if($scope.account.roles.includes('STORE_MANAGER')){
            if ($scope.account.countries[0] === 'CZ') {
                $scope.user.selectedCountry = $scope.countries[0];
            } else if ($scope.account.countries[0] === 'PL') {
                $scope.user.selectedCountry = $scope.countries[1];
            } else if ($scope.account.countries[0] === 'SK') {
                $scope.user.selectedCountry = $scope.countries[2];
            } else if ($scope.account.countries[0] === 'HU') {
                $scope.user.selectedCountry = $scope.countries[3];
            }
        }



        $scope.newUser = true;
        /**
         * Handler of form submit button.
         */
        $scope.submit = function(userForm) {
            $scope.messages.clear();
            if (userForm.$invalid) {
                return;
            }
            $scope.user.roles = [$scope.user.selectedRole.value];
            $scope.user.store = $scope.user.selectedStore;
            $scope.user.countries = [($scope.user.selectedCountry) ? $scope.user.selectedCountry.value : null];
            $scope.user.$save(
                function(value) {
                    $state.go('app.users.list').then(function() {
                        $rootScope.$broadcast(ovvEvents.SUCCESS_MSG,
                                $filter('translate')('administration.users.info.created'));
                    });
                },
                validationErrors.process([
                    { field: 'username', code: 'username.taken', msgKey: 'administration.users.error.usernameTaken' }
                ])
            );
        };

        $scope.clearStores = function () {
             $scope.user.stores = [];
        };
        $scope.refreshStores = function (store) {
            if (!store) {
                store = '';
            }
            var country = ($scope.user.selectedCountry?$scope.user.selectedCountry.value:'');
            if($scope.account.roles.includes('STORE_MANAGER')){
                country = $scope.account.countries[0];
            }
            return StoreResource.find({text: store, countryCode: country}).$promise.then(function (res) {
                $scope.storesList = res;
            });
        };
    }
})();

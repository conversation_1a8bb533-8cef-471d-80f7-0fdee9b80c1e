(function() {

    'use strict';

    angular.module('ovv.batch')
        .controller('BatchSummaryController', BatchSummaryController);
})();

/* @ngInject */
function BatchSummaryController($scope, $filter, TableAjax, $translate, StoreResource, $rootScope,
                             $httpParamSerializer, BatchResource, $modal, TransactionResource, ConfigService) {

  $scope.searchTimePeriod = ConfigService.getParams().transactionSearchTimeLimit;
    $scope.transactionTypes = [
        { value: 'VALIDATION', title: 'transaction.type.VALIDATION' },
        { value: 'REVERSAL', title: 'transaction.type.REVERSAL' }
    ];
    $scope.issuers = [
        'EDENRED',
        'LCHD',
        'SMART_VOUCHERS',
        'SODEXO',
        'TVM',
        'TESCO',
        'FandF',
        'WASA',
        'DOXX',
        'VASA',
        'LAHVOMAT',
        'FLASKOMAT',
        'NEMZETI_UTALVANY',
        'ERZSEBET',
        'POSTA'
    ];
    $scope.resultCodes = [
        { value: '0', title: 'batch.resultCode.0' },
        { value: '1', title: 'batch.resultCode.1' },
        { value: '2', title: 'batch.resultCode.2' },
        { value: '3', title: 'batch.resultCode.3' },
        { value: '4', title: 'batch.resultCode.4' },
        { value: '5', title: 'batch.resultCode.5' }
    ];

    $scope.datetimepickerOptionsFrom = {
            format: 'D.M.YYYY, HH:mm',
            useCurrent: 'day'
    };

    $scope.datetimepickerOptionsTo = {
            format: 'D.M.YYYY, HH:mm',
            useCurrent: false,
            defaultDate: moment().endOf('day')
    };

    var minusDays = function(date, daysCount) {
        return new Date(date.setDate(date.getDate() - daysCount));
    };

    /**
     * Search filter.
     */
    $scope.filter = {};

    if ($scope.account) {
        $scope.filter.countryCode = $scope.account.countries.length > 1 ? null : $scope.account.countries[0];
    }
    /**
     * Main search function invoked via the filter form.
     */
    $scope.searchTransactions = function() {
        $scope.table.instance.reloadData();
    };

    $scope.clearFilter = function() {
        $scope.filter = {};

        $scope.filter.countryCode = $scope.account.countries.length > 1 ? null : $scope.account.countries[0];

        var dateTo = moment().endOf('day').format('D.M.YYYY, HH:mm');
        $("#dateTimeTo").data("DateTimePicker").date(dateTo);
        $scope.filter.dateTimeTo = null;

        $scope.refreshStores();
    };



    var toIsoFormat = function(datetime, sourceFormat) {
        if (!datetime) {
            return null;
        }
        return moment(datetime, 'D.M.YYYY, HH:mm').format('YYYY-MM-DDTHH:mm:ss');
    };

    /**
     * Get object with search query parameters of the used filter.
     */
    var getSearchParams = function() {
        var issuers = [];
        if ($scope.filter.issuer && $scope.filter.issuer.length > 0) {
            $scope.filter.issuer.forEach(function (issuer) {
                issuers.push(issuer);
            });
        }
        return {
            countries: [($scope.filter.countryCode) ? $scope.filter.countryCode : null],
            issuer: issuers,
            fileName: $scope.filter.fileName ? $scope.filter.fileName.fileName : '',
            sentToIssuerFrom: toIsoFormat($scope.filter.dateTimeFrom),
            sentToIssuerTo: ($scope.filter.dateTimeTo?toIsoFormat(moment($scope.filter.dateTimeTo, 'D.M.YYYY, HH:mm').add(1,'minutes')):null),
            reconciliated: !!$scope.filter.reconciliated,
            voucherNumber: $scope.filter.voucherNumber,
            storeIds: [$scope.filter.store ? $scope.filter.store.id : null],
            resultCode: $scope.filter.resultCode ? $scope.filter.resultCode.value : null
        };
    };
    $scope.exportParams = function() {
        return $httpParamSerializer(getSearchParams());
    };


    /**
     *
     * Main table with list of transactions.
     */
    $scope.table = new TableAjax('api/batch/file', getSearchParams, $scope, 'batchFile')
        // Country Code
        .addColumn({
            data: 'countryCode',
            title: $translate('transaction.countryCode'),
            orderable: false
        })
        //ISSUER
        .addColumn({
            data: 'ovvIssuer',
            title: $translate('transaction.ovvIssuer'),
            orderable: false
        })
        //S/N
        .addColumn({
            data: 'fileName',
            title: $translate('batch.fileName'),
            orderable: false
        })
        // resultDescription
        .addColumn({
            data: 'itemsCount',
            title: $translate('batch.itemsCount'),
            orderable: false
        })
        // sent to issuer
        .addColumn({
            data: 'created',
            title: $translate('batch.created'),
            render: function(date) {
                return $filter('date')(date, 'd.M.yyyy, HH:mm:ss');
            },
            orderable: true
        })
        // Reconciliated
        .addColumn({
            data: 'reconciliated',
            title: $translate('batch.reconciliated'),
            render: function(date) {
                return $filter('date')(date, 'd.M.yyyy, HH:mm:ss');
            },
            orderable: false
        })
        .addColumn({
            data: 'fileName',
            title: $translate('batch.action'),
            render: function (data, type, full, meta) {
                return '<a class=\"btn btn-primary btn-sm\" ui-sref="app.batch.detail({fileName: \'' + data + '\'})">' +
                    $filter('translate')('batch.summary.detail') +'</a>';
            },
            orderable: false
        })
        .compileRows($scope)
        .sort([[4, 'desc']]);

    $scope.refreshStores = function (store) {
        if (!store) {
            store = '';
        }

        return StoreResource.find({text: store, countryCode: $scope.filter.countryCode }).$promise.then(function (res) {
            $scope.stores = res;
        });
    };

    $scope.fromAfterBefore = function() {
        var from = $scope.filter.dateTimeFrom;
        var to = $scope.filter.dateTimeTo;
        var datetimeFrom = moment(from, 'D.M.YYYY');
        var datetimeTo = moment(to, 'D.M.YYYY');
        return datetimeFrom.isAfter(datetimeTo);
    };

    $scope.refreshFileNames = function (fileName) {
        if (!fileName) {
            fileName = '';
        }

        return BatchResource.findFileName({text: fileName}).$promise.then(function (res) {
            $scope.fileNames = res;
        });
    };
}

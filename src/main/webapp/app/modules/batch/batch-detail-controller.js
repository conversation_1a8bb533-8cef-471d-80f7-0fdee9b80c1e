(function() {

    'use strict';

    angular.module('ovv.batch')
        .controller('BatchDetailController', BatchDetailController);
})();

/* @ngInject */
function BatchDetailController($scope, $filter, TableAjax, $translate, StoreResource, $rootScope,
                             $httpParamSerializer, BatchResource, $modal, TransactionResource, $stateParams, ConfigService) {

  $scope.searchTimePeriod = ConfigService.getParams().transactionSearchTimeLimit;
    $scope.transactionTypes = [
        { value: 'VALIDATION', title: 'transaction.type.VALIDATION' },
        { value: 'REVERSAL', title: 'transaction.type.REVERSAL' }
    ];
    $scope.issuers = [
        'EDENRED',
        'LCHD',
        'SMART_VOUCHERS',
        'SODEXO',
        'TVM',
        'TESCO',
        'FandF',
        'WASA',
        'DOXX',
        'VASA',
        'LAHVOMAT',
        'FLASKOMAT',
        'NEMZETI_UTALVANY',
        'ERZSEBET',
        'POSTA'
    ];
    $scope.resultCodes = [
        { value: '0', title: 'batch.resultCode.0' },
        { value: '1', title: 'batch.resultCode.1' },
        { value: '2', title: 'batch.resultCode.2' },
        { value: '3', title: 'batch.resultCode.3' },
        { value: '4', title: 'batch.resultCode.4' },
        { value: '5', title: 'batch.resultCode.5' }
    ];

    $scope.datetimepickerOptionsFrom = {
            format: 'D.M.YYYY, HH:mm',
            useCurrent: 'day'
    };

    $scope.datetimepickerOptionsTo = {
            format: 'D.M.YYYY, HH:mm',
            useCurrent: false,
            defaultDate: moment().endOf('day')
    };

    var minusDays = function(date, daysCount) {
        return new Date(date.setDate(date.getDate() - daysCount));
    };


    $scope.account = $rootScope.account;

    $rootScope.$watch('account', function (acc) {
        if (acc) {
            $scope.account = acc;
            if ($scope.table.instance.reloadData) {
                $scope.table.instance.reloadData();
            }
        }
    });

    /**
     * Search filter.
     */
    $scope.filter = {};

    if ($scope.account) {
        $scope.filter.countryCode = $scope.account.countries.length > 1 ? null : $scope.account.countries[0];
    }

    if ($stateParams.fileName) {
        $scope.filter.fileName = {
            fileName: $stateParams.fileName
        }
    }
    /**
     * Main search function invoked via the filter form.
     */
    $scope.searchTransactions = function() {
        $scope.table.instance.reloadData();
    };

    $scope.clearFilter = function() {
        $scope.filter = {};
        $scope.filter.countryCode = $scope.account.countries.length > 1 ? null : $scope.account.countries[0];

        var dateTo = moment().endOf('day').format('D.M.YYYY, HH:mm');
        $("#dateTimeTo").data("DateTimePicker").date(dateTo);
        $scope.filter.dateTimeTo = null;

        $scope.refreshStores();
    };

    var toIsoFormat = function(datetime, sourceFormat) {
        if (!datetime) {
            return null;
        }
        return moment(datetime, 'D.M.YYYY, HH:mm').format('YYYY-MM-DDTHH:mm:ss');
    };

    /**
     * Get object with search query parameters of the used filter.
     */
    var getSearchParams = function() {
        var issuers = [];
        if ($scope.filter.issuer && $scope.filter.issuer.length > 0) {
            $scope.filter.issuer.forEach(function (issuer) {
                issuers.push(issuer);
            });
        }
        return {
            countries: [($scope.filter.countryCode) ? $scope.filter.countryCode : null],
            issuer: issuers,
            fileName: $scope.filter.fileName ? $scope.filter.fileName.fileName : '',
            sentToIssuerFrom: toIsoFormat($scope.filter.dateTimeFrom),
            sentToIssuerTo: ($scope.filter.dateTimeTo?toIsoFormat(moment($scope.filter.dateTimeTo, 'D.M.YYYY, HH:mm').add(1,'minutes')):null),
            reconciliated: !!$scope.filter.reconciliated,
            voucherNumber: $scope.filter.voucherNumber,
            storeIds: [$scope.filter.store ? $scope.filter.store.id : null],
            resultCode: $scope.filter.resultCode ? $scope.filter.resultCode.value : null
        };
    };
    $scope.exportParams = function() {
        return $httpParamSerializer(getSearchParams());
    };


    /**
     *
     * Main table with list of transactions.
     */
    $scope.table = new TableAjax('api/batch/', getSearchParams, $scope)
    // Country Code
        .addColumn({
            data: 'reportFile.countryCode',
            title: $translate('transaction.countryCode'),
            orderable: false
        })
        //ISSUER
        .addColumn({
            data: 'reportFile.ovvIssuer',
            title: $translate('transaction.ovvIssuer'),
            orderable: false
        })
        // // S/N
        .addColumn({
            data: 'voucherNumber',
            title: $translate('transaction.voucherNumber'),
            orderable: false
        })
        .addColumn({
            data: function(row) {
                if (!!row.store) {
                    return row.store.costCentre + "/" + row.store.siteCode;
                } else {
                    return "";
                }

            },
            title: $translate('batch.costCentreSiteCode'),
            orderable: false
        })
        .addColumn({
            data: 'resultCode',
            title: $translate('transaction.resultCode'),
            render: function(code) {
                if (code != null) {
                    return '<span class="label">&nbsp;' + code + '&nbsp;</span>';
                }
                return '';
            },
            createdCell: function(cell, cellData, rowData, rowIndex, colIndex) {
                var labelClass = returnColor(rowData.resultCode);
                angular.element(cell).find('span').addClass(labelClass);
            },
            orderable: false
        })
        // resultDescription
        .addColumn({
            data: 'resultDescription',
            title: $translate('batch.resultDescription'),
            orderable: false
        })

        // sent to issuer
        .addColumn({
            data: 'reportFile.created',
            title: $translate('batch.created'),
            render: function(date) {
                return $filter('date')(date, 'd.M.yyyy, HH:mm:ss');
            },
            orderable: true
        })
        // Reconciliated
        .addColumn({
            data: 'reportFile.reconciliated',
            title: $translate('batch.reconciliated'),
            render: function(date) {
                return date ? $filter('date')(date, 'd.M.yyyy, HH:mm:ss') : $filter('translate')('common.filter.NO');
            },
            orderable: false
        })
        // action
        .addColumn({
            data: 'transactionRequestId',
            title: $translate('batch.detail'),
            render: function (data, type, full, meta) {
                return '<a ng-click="showDetail(' + data + ')\" ' +
                    + 'data-tooltip="' + $filter('translate')('batch.detail') + '">'
                    + '<i class="fa fa-fw fa-info"></i></a>';
            },
            orderable: false
        })
        .compileRows($scope)
        .sort([[5, 'desc']]);

    $scope.refreshStores = function (store) {
        if (!store) {
            store = '';
        }

        return StoreResource.find({text: store, countryCode: $scope.filter.countryCode }).$promise.then(function (res) {
            $scope.stores = res;
        });
    };

    $scope.refreshFileNames = function (fileName) {
        if (!fileName) {
            fileName = '';
        }

        return BatchResource.findFileName({text: fileName}).$promise.then(function (res) {
            $scope.fileNames = res;
        });
    };

    $scope.showDetail = function (id) {
        $modal.open({
            templateUrl: 'app/modules/transactions/transaction-detail.html',
            controller: 'TransactionDetailController',
            // controllerAs: 'vm',
            size: 'lg',
            resolve: {
                transaction: function () {
                    return TransactionResource.get({id: id}).$promise.then(function (response) {
                        return response;
                    }, function (error) {
                        return null;
                    });
                },
                transactionId: function () {
                    return id;
                }
            }
        });
    };

    $scope.returnColor = returnColor;

    $scope.fromAfterBefore = function() {
        var from = $scope.filter.dateTimeFrom;
        var to = $scope.filter.dateTimeTo;
        var datetimeFrom = moment(from, 'D.M.YYYY');
        var datetimeTo = moment(to, 'D.M.YYYY');
        return datetimeFrom.isAfter(datetimeTo);
    };

    function returnColor(code) {
        if (code != null) {
            code = code.toString();
        }
        switch (code) {
            case '0':
                return 'label-success';
            case '1':
                return 'label-warning';
            case '2':
            case '3':
            case '4':
            case '5':
            default:
                return 'label-danger';
        }
    }
}

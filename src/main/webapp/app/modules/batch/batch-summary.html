<h1 class="page-title" translate>batch.summary.heading</h1>

<section class="widget vm-filter">
    <div class="widget-body">
       <div class="alert alert-sm alert-danger" ng-show="fromAfterBefore()">
                   <span translate>transaction.dateOrderInvalid</span>
          </div>
        <form>
            <div class="row">
                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="dateTimeFrom" translate>common.filter.createdFrom</label>
                    <input type="text" autocomplete="off" datetimepicker datetimepicker-options="{{datetimepickerOptionsFrom}}"
                           id="dateTimeFrom" name="dateTimeFrom"
                           class="form-control" ng-model="filter.dateTimeFrom" />
                </div>
                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="dateTimeTo" translate>common.filter.createdTo</label>
                    <input type="text" autocomplete="off" datetimepicker datetimepicker-options="{{datetimepickerOptionsTo}}"
                           id="dateTimeTo" name="dateTimeTo"
                           class="form-control" ng-model="filter.dateTimeTo" />
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="countryCode" translate>transaction.countryCode</label>
                    <ui-select id="countryCode" name="countryCode" class="form-control"
                               ng-model="filter.countryCode" search-enabled="false"
                               on-select="refreshStores($select.search)">
                        <ui-select-match allow-clear="true">
                            {{$select.selected}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in account.countries">
                            {{item}}
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="issuer" translate>transaction.ovvIssuer</label>
                    <ui-select multiple id="issuer" name="issuer" class="form-control"
                               ng-model="filter.issuer" search-enabled="false" >
                        <ui-select-match allow-clear="true">
                            {{$item}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in issuers">
                            {{item}}
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-3 col-md-2 checkbox">
                    &nbsp;&nbsp;&nbsp;<input type="checkbox" id="reconciliated" name="reconciliated"
                                             ng-model="filter.reconciliated">
                    <label for="reconciliated" translate>batch.reconciliated</label>
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="fileName" translate>batch.fileName</label>
                    <ui-select id="fileName" name="fileName" class="form-control"
                               ng-model="filter.fileName" search-enabled="true">
                        <ui-select-match allow-clear="true">
                            {{$select.selected.fileName}}
                        </ui-select-match>
                        <ui-select-choices repeat="fileName in fileNames track by $index"
                                           refresh="refreshFileNames($select.search)"
                                           refresh-delay="2">
                            {{fileName.fileName}}
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
            <div class="btn-toolbar">
                <button type="submit" class="btn btn-sm btn-primary" ng-click="searchTransactions()" ng-disabled="intervalToBig()||fromAfterBefore()">
                    <i class="fa fa-search mr-xs"></i>
                    <span translate>common.filter.search</span>
                </button>
                <button type="button" class="btn btn-sm btn-gray" ng-click="clearFilter()">
                    <i class="fa fa-times text-danger mr-xs"></i>
                    <span translate>common.filter.reset</span>
                </button>
            </div>
        </form>
    </div>
</section>

<section class="widget">
    <div class="widget-body">
        <table datatable dt-options="table.options" dt-columns="table.columns" dt-instance="table.instance"
                class="table table-striped table-condensed table-hover"></table>
    </div>
</section>

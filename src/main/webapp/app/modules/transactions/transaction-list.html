<h1 class="page-title" translate>transactions.list.heading</h1>

<section class="widget vm-filter">
    <div class="widget-body">
          <div class="alert alert-sm alert-danger" ng-show="fromAfterBefore()">
                   <span translate>transaction.dateOrderInvalid</span>
          </div>
          <div class="alert alert-sm alert-danger" ng-show="dateInFuture()">
                   <span translate>transaction.intervalDateInFuture </span>
          </div>

        <form>
            <div class="row">
                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="dateTimeFrom" translate>common.filter.dateFrom</label>
                    <input type="text" autocomplete="off" datetimepicker datetimepicker-options="{{datetimepickerOptionsFrom}}"
                            id="dateTimeFrom" name="dateTimeFrom"
                            class="form-control" ng-model="filter.dateTimeFrom"/>
                </div>
                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="dateTimeTo" translate>common.filter.dateTo</label>
                    <input type="text" autocomplete="off" datetimepicker datetimepicker-options="{{datetimepickerOptionsTo}}"
                            id="dateTimeTo" name="dateTimeTo"
                            class="form-control" ng-model="filter.dateTimeTo" />
                </div>
                <div class="col-sm-1 col-md-1 form-group">
                    <label for="countryCode" translate>transaction.countryCode</label>
                    <ui-select id="countryCode" name="countryCode" class="form-control"
                            ng-model="filter.countryCode" search-enabled="false" on-select="refreshStores($select.search)">
                        <ui-select-match allow-clear="true">
                            {{$select.selected}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in account.countries">
                            {{item}}
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-2 col-md-1 form-group">
                    <label for="stan" translate>transaction.stan</label>
                    <input type="text" id="stan" name="stan" class="form-control"
                            ng-model="filter.stan">
                </div>
                <div class="col-sm-3 col-md-2 form-group">
                    <label for="deviceId" translate>transaction.deviceId</label>
                    <input type="text" id="deviceId" name="deviceId" class="form-control"
                           ng-model="filter.deviceId">
                </div>
                <div class="col-sm-3 col-md-2 form-group">
                    <label for="type" translate>transaction.type</label>
                    <ui-select id="type" name="type" class="form-control"
                            ng-model="filter.type" search-enabled="false" >
                        <ui-select-match allow-clear="true">
                            {{$select.selected.title | translate}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in transactionTypes">
                            {{item.title | translate}}
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="voucherNumber" translate>transaction.voucherNumber</label>
                    <input type="text" id="voucherNumber" name="voucherNumber" class="form-control"
                            ng-model="filter.voucherNumber">
                </div>
                <div class="col-sm-3 col-md-2 form-group">
                    <label for="store" translate>transaction.store</label>
                    <ui-select id="store" name="store" class="form-control" ng-model="filter.store" search-enabled="true">
                        <ui-select-match allow-clear="true">
                            {{$select.selected.siteCode + ' ' + $select.selected.name}}
                        </ui-select-match>
                        <ui-select-choices repeat="store in stores track by $index"
                                           refresh="refreshStores($select.search)"
                                           refresh-delay="2">
                            {{store.siteCode + ' ' + store.name}}
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="expirationYear" translate>common.filter.expirationYear</label>
                    <input type="text" datetimepicker datetimepicker-options="{{expirationYearDatetimepickerOptions}}"
                            id="expirationYear" name="expirationYear"
                            class="form-control" ng-model="filter.expirationYear"/>
                </div>

                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="trainingMode" translate>common.filter.trainingMode</label>
                    <ui-select id="trainingMode" name="trainingMode" class="form-control"
                               ng-model="filter.trainingMode" search-enabled="false" >
                        <ui-select-match allow-clear="false">
                            {{$select.selected.title}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in trainingModes">
                            {{item.title}}
                        </ui-select-choices>
                    </ui-select>
                </div>

                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="offlineMode" translate>transaction.offlineMode</label>
                    <ui-select id="offlineMode" name="offlineMode" class="form-control"
                               ng-model="filter.offlineMode" search-enabled="false" >
                        <ui-select-match allow-clear="false">
                            {{$select.selected.title}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in offlineModes">
                            {{item.title}}
                        </ui-select-choices>
                    </ui-select>
                </div>

                <div class="col-sm-4 col-md-3 col-lg-2 form-group">
                    <label for="manualRedemption" translate>transaction.manualRedemption</label>
                    <ui-select id="manualRedemption" name="manualRedemption" class="form-control"
                               ng-model="filter.manualRedemption" search-enabled="false" >
                        <ui-select-match allow-clear="false">
                            {{$select.selected.title}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in manualRedemptions">
                            {{item.title}}
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="issuer" translate>transaction.ovvIssuer</label>
                    <ui-select multiple id="issuer" name="issuer" class="form-control"
                               ng-model="filter.issuer" search-enabled="false" >
                        <ui-select-match allow-clear="true">
                            {{$item}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in issuers">
                            {{item}}
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="resultCode" translate>transaction.resultCode</label>
                    <ui-select multiple id="resultCode" name="resultCode" class="form-control"
                               ng-model="filter.resultCode" search-enabled="false" >
                        <ui-select-match allow-clear="true">
                            {{$item.title | translate}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in resultCodes ">
                            {{item.title | translate}}
                        </ui-select-choices>
                    </ui-select>
                </div>

            </div>
            <div class="btn-toolbar">
                <button type="submit" class="btn btn-sm btn-primary" ng-click="searchTransactions()" ng-disabled="intervalToBig()||fromAfterBefore()">
                    <i class="fa fa-search mr-xs"></i>
                    <span translate>common.filter.search</span>
                </button>
                <button type="button" class="btn btn-sm btn-gray" ng-click="clearFilter()">
                    <i class="fa fa-times text-danger mr-xs"></i>
                    <span translate>common.filter.reset</span>
                </button>
                <a href="api/transactions/xls?{{exportParams()}}" target="_blank" class="btn btn-sm btn-default">
                    <span translate>common.filter.xlsExport</span>
                </a>
            </div>
        </form>
    </div>
</section>

<section class="widget">
    <div class="widget-body">
        <table datatable dt-options="table.options" dt-columns="table.columns" dt-instance="table.instance"
                class="table table-striped table-condensed table-hover"></table>
    </div>
</section>

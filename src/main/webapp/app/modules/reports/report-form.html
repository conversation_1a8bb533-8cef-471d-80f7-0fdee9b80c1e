<h1 class="page-title" translate>reports.new.heading</h1>

<vm-messages messages="messages"></vm-messages>

<vm-section>
    <vm-section-title>
        <translate>reports.new.title</translate>
    </vm-section-title>
    <vm-section-body>
        <form class="form-horizontal" name="form" novalidate>
            <div class="row">
                <div class="col-md-11 col-lg-8">
                    <fieldset>
                        <div class="form-group">
                            <label for="type" class="col-sm-3 control-label" translate>report.type</label>
                            <div class="col-sm-8">
                                <ui-select id="type" name="type" class="form-control"
                                           ng-model="params.type" required>
                                    <ui-select-match>
                                        {{$select.selected.title | translate}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="type in reportTypes">
                                        <div ng-bind-html="type.title | translate"></div>
                                    </ui-select-choices>
                                </ui-select>
                                <vm-input-errors field="form.type" required/>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <fieldset>
                <legend ng-if="params.type" translate>reports.new.parameters</legend>
                <div class="row">
                    <div class="col-md-11 col-lg-8">
                        <div class="row form-group" ng-if="isParamDefined('period')">
                            <label for="dateFrom" class="col-sm-3 control-label"
                                   translate>common.filter.dateFrom</label>
                            <div class="col-sm-8">
                            <input type="text" datetimepicker datetimepicker-options="{{datetimepickerOptions}}"
                                   id="dateFrom" name="dateFrom"
                                   class="form-control" ng-model="params.dateFrom" required/>
                            <vm-input-errors field="form.dateFrom" required/>
                            </div>
                        </div>
                        <div class="form-group" ng-if="isParamDefined('period')">
                                <label for="dateTo" class="col-sm-3 control-label"
                                       translate>common.filter.dateTo</label>
                            <div class="col-sm-8">
                                <input type="text" datetimepicker datetimepicker-options="{{datetimepickerOptions}}"
                                       id="dateTo" name="dateTo"
                                       class="form-control" ng-model="params.dateTo" required/>
                            </div>
                                <vm-input-errors field="form.dateTo" required/>
                        </div>
                        <div class="row form-group" ng-if="isParamDefined('store')">
                            <label for="store" class="col-sm-3 control-label" translate>report.params.store</label>
                            <div class="col-sm-8">
                                <ui-select id="store" name="store" class="form-control" ng-model="params.selectedStore" search-enabled="true" required>
                                    <ui-select-match>
                                        {{$select.selected.siteCode + ' ' + $select.selected.name}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="store in stores track by $index"
                                                       refresh="refreshStores($select.search)"
                                                       refresh-delay="2">
                                        {{store.siteCode + ' ' + store.name}}
                                    </ui-select-choices>
                                </ui-select>
                                <vm-input-errors field="form.store" required/>
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>

            <div class="form-actions btn-toolbar text-align-center">
                <button type="submit" class="btn btn-primary" ng-click="submit()" ng-disabled="form.$invalid" translate>
                    common.submit
                </button>
                <a type="button" class="btn btn-inverse" data-ui-sref="app.reports.list" translate>common.cancel</a>
            </div>
        </form>
    </vm-section-body>
</vm-section>

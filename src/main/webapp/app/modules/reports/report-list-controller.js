(function () {
    'use strict';

    angular.module('ovv.reports')
        .controller('ReportListController', ReportListController);


    function ReportListController($scope, $rootScope, $filter, StoreResource, TableAjax, DateUtils) {


        $rootScope.$watch('account', function (acc) {
            if (acc) {
                $scope.account = acc;
                if ($scope.table.instance.reloadData) {
                    $scope.table.instance.reloadData();
                }
            }
        });

        $scope.datetimepickerOptions = {
            // defaultDate: moment().subtract(1, 'days'),
            format: 'D.M.YYYY'
        };

        /**
         * Search filter.
         */
        $scope.filter = {};

        $scope.reportTypes = [
            { type: 'USED_VOUCHERS', title: 'report.type.USED_VOUCHERS'},
            { type: 'CATEGORY', title: 'report.type.CATEGORY'}
        ];

        $scope.reportStatuses = [
            { type: 'QUEUED', title: 'report.status.QUEUED' },
            { type: 'IN_PROGRESS', title: 'report.status.IN_PROGRESS' },
            { type: 'OK', title: 'report.status.OK' },
            { type: 'FAILED', title: 'report.status.FAILED' }
        ];

        $scope.refreshStores = function (store) {
            if (!store) {
                store = '';
            }

            return StoreResource.find({
                text: store,
                countryCode: $scope.filter.countryCode,
                searchScope: 'USER'
            }).$promise.then(function (res) {
                $scope.stores = res;
            });
        };

        /**
         * Main search function invoked via the filter form.
         */
        $scope.searchReports = function () {
            $scope.table.instance.reloadData();
        };

        $scope.clearFilter = function () {
            $scope.filter = {};
            $scope.refreshStores();
        };

        /**
         * Get object with search query parameters of the used filter.
         */
        var getSearchParams = function () {
            return {
                type: $scope.filter.type ? $scope.filter.type.type : null,
                status: $scope.filter.status ? $scope.filter.status.type : null,
                storeIds: [$scope.filter.store ? $scope.filter.store.id : null],
                dateFrom: DateUtils.toISOLocalDate($scope.filter.dateFrom),
                dateTo: DateUtils.toISOLocalDate($scope.filter.dateTo)
            };
        };

        var getActionRowButtons = function (report) {
            if (report.status !== 'OK') {
                return '';
            }
            var actionCell = '<div class="vm-table-row-buttons"><div class="vm-actionsb text-success">';
            actionCell += '<a ng-href="api/reports/' + report.id + '/data" '
                + 'data-tooltip="' + $filter('translate')('reports.list.downloadReport') + '">'
                + '<i class="fa fa-fw fa-download"></i></a>';
            actionCell += '</div></div>';
            return actionCell;
        };

        /**
         * Main report table.
         */
        $scope.table = new TableAjax('api/reports/', getSearchParams)
        // Created Date
            .addColumn({
                data: 'createdDate',
                render: function (date) {
                    return $filter('date')(date, 'dd.MM.yyyy, HH:mm');
                },
                title: $filter('translate')('report.createdDate')
            })
            // Report Status
            .addColumn({
                data: 'status',
                render: function (status) {
                    return $filter('translate')('report.status.' + status);
                },
                title: $filter('translate')('report.status'),
                orderable: false,
                createdCell: function (cell, cellData, rowData, rowIndex, colIndex) {
                    if (rowData.status === 'OK') {
                        angular.element(cell).addClass('fw-semi-bold text-success');
                    } else if (rowData.status === 'FAILED' || rowData.status === 'LARGE_EXPORT') {
                        angular.element(cell).addClass('fw-semi-bold text-danger');
                    }
                }
            })
            // Report Type
            .addColumn({
                data: 'type',
                render: function (type) {
                    return $filter('translate')('report.type.' + type);
                },
                title: $filter('translate')('report.type'),
                orderable: false
            });

        // Store(s)
        $scope.table.addColumn({
            data: 'store',
            render: function (store) {
                return store.name;
            },
            title: $filter('translate')('report.store'),
            orderable: false
        });
        // Params
        $scope.table.addColumn({
            data: function(row){
                return $filter('date')(row.dataFrom, 'dd.MM.yyyy') + " - " + $filter('date')(row.dataTo, 'dd.MM.yyyy');
            },
            title: $filter('translate')('reports.new.parameters'),
            orderable: false
        })
        // Download link
        $scope.table.addColumn({
            data: function (row) {
                return getActionRowButtons(row);
            },
            orderable: false,
            title: ''
        });

        $scope.table
            .compileRows($scope)
            .sort([[0, 'desc']]);
    }
})();

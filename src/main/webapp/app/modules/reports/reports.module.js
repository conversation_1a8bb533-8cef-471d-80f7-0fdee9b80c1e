'use strict';

angular.module('ovv.reports', ['ovv.common.validation'])

.config(['$stateProvider',
    function appConfig($stateProvider) {
        $stateProvider
            .state('app.reports', {
                abstract: true,
                url: '/reports',
                template: '<ui-view/>'
            })
            .state('app.reports.list', {
                url: '/',
                templateUrl: 'app/modules/reports/report-list.html',
                controller: 'ReportListController'
            })
            .state('app.reports.new', {
                url: '/new',
                templateUrl: 'app/modules/reports/report-form.html',
                controller: 'ReportNewController'
            });
    }]);

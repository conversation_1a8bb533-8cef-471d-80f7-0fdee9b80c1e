'use strict';

angular.module('ovv.reports')

    .controller('ReportNewController', ['$scope', '$filter', '$rootScope', '$state',
        'StoreResource', 'ReportResource', 'DateUtils', 'validationErrors',
        function ($scope, $filter, $rootScope, $state,
                  StoreResource, ReportResource, DateUtils, validationErrors) {

            $scope.reportTypes = [
                {type: 'USED_VOUCHERS', title: 'report.type.USED_VOUCHERS', params: ['period', 'store']},
                {type: 'CATEGORY', title: 'report.type.CATEGORY', params: ['period', 'store']}
            ];

            var configs = $scope.reportTypes;

            // Sort configs by their type into map
            var configMap = {};
            configs.reduce(function (map, config) {
                map[config.type] = config;
                return map;
            }, configMap);

            /**
             * If the given report parameter is defined for the currently selected report type.
             */
            $scope.isParamDefined = function (paramName) {
                if (!$scope.params.type) {
                    return false;
                }
                return configMap[$scope.params.type.type].params.indexOf(paramName) >= 0;
            };

            /**
             * Input parameters of new report.
             */
            $scope.params = {};

            $scope.account = $rootScope.account;

            $scope.datetimepickerOptions = {
                // defaultDate: moment().subtract(1, 'days'),
                format: 'D.M.YYYY'
            };

            $scope.refreshStores = function (store) {
                if (!store) {
                    store = '';
                }

                return StoreResource.find({
                    text: store,
                    searchScope: 'USER'
                }).$promise.then(function (res) {
                    $scope.stores = res;
                });
            };

            $scope.submit = function () {
                var postData = {
                    type: $scope.params.type.type,
                    params: {}
                };
                if ($scope.isParamDefined('period')) {
                    postData.params.fromDate = DateUtils.toISOLocalDate($scope.params.dateFrom);
                    postData.params.toDate = DateUtils.toISOLocalDate($scope.params.dateTo);
                }
                if ($scope.isParamDefined('store') && $scope.params.selectedStore) {
                    postData.params.storeId = $scope.params.selectedStore.id;
                }

                ReportResource.save(null, postData,
                    function (response) {
                        $state.go('app.reports.list').then(function () {
                            $scope.messages.success($filter('translate')('reports.new.info.success'));
                        });
                    },
                    validationErrors.process([
                        {field: 'dateInterval', code: 'object.invalid', msgKey: 'reports.dateInterval.invalid'},
                        {field: 'dateInterval', code: 'tooBig', msgKey: 'reports.dateInterval.tooBig'}
                    ])
                );
            };
        }]);

<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title th:text="#{app.title}">OVV Viewer</title>
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/vendor-d96f386b99.css}">
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/app-bbd0b7a4b2.css}">
</head>

<body class="login-page">
    <div class="container">
        <main id="content" class="widget-login-container" role="main">
        <div class="row">
            <div class="col-lg-4 col-sm-6 col-xs-10 col-lg-offset-4 col-sm-offset-3 col-xs-offset-1">
                <h4 class="widget-login-logo animated fadeInUp">
                    <i class="fa fa-circle text-gray"></i><span th:text="#{login.title}">OVV Viewer</span><i class="fa fa-circle text-warning"></i>
                </h4>
                <section class="widget widget-login animated fadeInUp">
                    <header>
                        <h3 class="text-align-center" th:text="#{login.header}">Login to OVV Viewer</h3>
                    </header>
                    <div class="widget-body">

                        <div class="alert alert-sm alert-danger" th:if="${param.error}" th:text="#{login.ssoError}">
                            Unable to login with Tesco account
                        </div>
                        <div class="alert alert-sm alert-success" th:if="${param.logout}" th:text="#{login.loggedOut}">
                            Logged out
                        </div>
                        <div class="alert alert-sm alert-danger" th:if="${param.expired}" th:text="#{login.sessionExpired}">
                            Session expired
                        </div>
                        <form th:action="@{/oauth2/authorization/default}">
                            <div class="clearfix text-align-center">
                                <button type="submit" class="btn btn-inverse btn-sm">
                                    <i class="fa fa-sign-in"></i>
                                    <span th:text="#{login.sso.submit}">Login</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </section>
            </div>
        </div>
        </main>
        <footer class="page-footer"><span th:text="${currentYear}">2017</span> &copy;, Diebold Nixdorf</footer>
    </div>
</body>
</html>

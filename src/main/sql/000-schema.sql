CREATE TABLE ovv_user (
    id bigserial NOT NULL,
    created_date timestamp NOT NULL,
    modified_date timestamp NOT NULL,
    active boolean NOT NULL,
    email varchar(128),
    first_name varchar(64),
    last_name varchar(64),
    password varchar(64) NOT NULL,
    role varchar(20) NOT NULL,
    username varchar(64) NOT NULL,
    modified_by_user_id int8 NOT NULL,
    country_code varchar(2),
    is_account_non_expired boolean NOT NULL DEFAULT '1',
    PRIMARY KEY (id)
);

ALTER TABLE ovv_user ADD CONSTRAINT uk_ovv_user_username UNIQUE (username);

ALTER TABLE ovv_user ADD CONSTRAINT fk_ovv_user_modified_by FOREIGN KEY (modified_by_user_id) REFERENCES ovv_user;

CREATE TABLE repl_ovv_transaction
(
  id bigint NOT NULL,
  device_id character varying(255) NOT NULL,
  device_local_date_time timestamp without time zone NOT NULL,
  module character varying(255) NOT NULL,
  server_local_date_time timestamp without time zone NOT NULL,
  server_utc_date_time timestamp without time zone NOT NULL,
  amount bigint,
  client_ip_address character varying(255),
  country_code character varying(16) NOT NULL,
  offline_mode boolean NOT NULL,
  ovv_issuer character varying(255),
  partner_id character varying(255),
  payment_place character varying(255),
  pos_country_code smallint,
  pos_currency_code smallint,
  repeated boolean NOT NULL,
  stan integer,
  transaction_type character varying(32) NOT NULL,
  voucher_number character varying(255),
  previous_request_id bigint,
  error_description character varying(255),
  result_code character varying(32) NOT NULL,
  validation_host character varying(255),
  operator character varying(255),
  expiration_date_time timestamp without time zone,
  manual_redemption boolean default FALSE,
  training_mode boolean default FALSE,
  category character varying(16),
  CONSTRAINT ovv_transaction_pkey PRIMARY KEY (id)
);

create table OVV_STORE (
  id bigint not null,
  cost_centre varchar(255) not null,
  country_code varchar(2) not null,
  name varchar(255) not null,
  site_code varchar(255) not null,
  partner_id varchar(255) not null,
  partner_group varchar(255) not null,
  primary key (id)
);

ALTER TABLE OVV_STORE ADD CONSTRAINT uk_ovv_store_cc_pi_sc UNIQUE (country_code, partner_id, site_code);



CREATE TABLE ovv_user_store (
    id bigserial NOT NULL,
    user_id bigint NOT NULL,
    store_id bigint NOT NULL,
    primary key (id)
);

ALTER TABLE ovv_user_store ADD CONSTRAINT fk_ovv_store_store_id FOREIGN KEY (store_id) REFERENCES ovv_store;
ALTER TABLE ovv_user_store ADD CONSTRAINT fk_ovv_user_user_id FOREIGN KEY (user_id) REFERENCES ovv_user;



create table REPORT_BATCH (
    id bigint NOT NULL,
    ovv_transaction_request_id bigint,
    result_code smallint,
    result_description character varying(255),
    voucher_number character varying(255),
    report_file_id bigint NOT NULL,
    store_id bigint NOT NULL,
    primary key (id)
);

create table REPORT_FILE (
    id bigint NOT NULL,
    country_code character varying(5) not null,
    created timestamp without time zone NOT NULL,
    file_name character varying(255) not null,
    filename_date character varying(255) not null,
    file_path character varying(255) not null,
    items_count integer not null,
    ovv_issuer character varying(255) not null,
    reconciliated  timestamp without time zone,
    uuid bigint not null,
    primary key (id)
);

create table ovv_cash_register (
  id bigserial not null,
  number varchar(255) not null,
  site_code varchar(255) not null,
  active boolean NOT NULL,
  import_date timestamp without time zone NOT NULL,
  primary key (id)
);

ALTER TABLE ovv_cash_register ADD CONSTRAINT uk_ovv_cash_register_number_sitecode_active UNIQUE (number, site_code, active);

create table REPORT
(
    id                 bigserial                   not null,
    created_date       timestamp without time zone not null,
    data               oid,
    modified_date      timestamp without time zone not null,
    params             character varying(255),
    status             varchar(16)                 not null,
    type               varchar(32)                 not null,
    created_by_user_id bigint,
    created_by_user    varchar(64),
    store_id           bigint,
    primary key (id)
);

alter table REPORT
    add constraint FK_REPORT_CREATED_BY_USER_ID
        foreign key (created_by_user_id)
            references ovv_user;

alter table REPORT
    add constraint FK_REPORT_STORE_ID
        foreign key (store_id)
            references ovv_store;

create table VOUCHER
(
    id                             bigint                 not null,
    country_code                   character varying(16)  NOT NULL,
    server_local_date_time         timestamp              not null,
    voucher_number                 character varying(255) not null,
    voucher_state                  character varying(32)  not null,
    related_transaction_request_id bigint,
    expiration_date_time           timestamp,
    primary key (id)
);


ALTER TABLE report_batch OWNER to ovv;
ALTER TABLE report_file OWNER to ovv;
ALTER TABLE ovv_store OWNER to ovv;
ALTER TABLE repl_ovv_transaction OWNER to ovv;
ALTER TABLE ovv_user OWNER to ovv;
ALTER TABLE ovv_user_store OWNER to ovv;
ALTER TABLE ovv_cash_register OWNER to ovv;
ALTER TABLE REPORT OWNER to ovv;
ALTER TABLE VOUCHER OWNER to ovv;

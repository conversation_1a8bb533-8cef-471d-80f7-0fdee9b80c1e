jdbc.driver = org.postgresql.Driver
jdbc.url = **************************************
jdbc.username = postgres
jdbc.password = postgres

# If this is a number greater than 0, the application will test all idle,
# pooled but unchecked-out connections, every this number of seconds.
ds.connectionTestPeriod = 60
# Query to be executed for connection tests
ds.testQuery = select 1
# Minimum number of Connections a pool will maintain at any given time.
ds.minPoolSize = 5
# Maximum number of Connections a pool will maintain at any given time.
ds.maxPoolSize = 30
# Seconds a Connection can remain pooled but unused before being discarded.
ds.maxIdleTime = 120

# Global application messages
app.title = Tesco OVV Prehliada\u010D transakci\u00ED
app.name = OVV Prehliada\u010D transakci\u00ED
app.fullName = OVV Prehliada\u010D
app.shortName = OVV

# Navigation bar
navbar.myAccount = M\u00F4j \u00FA\u010Det
navbar.changePassword = Zmeni\u0165 heslo
navbar.logout = Odhl\u00E1si\u0165 sa

# Login page
login.title = OVV Prehliada\u010D transakci\u00ED
login.header = Prihl\u00E1si\u0165 sa do OVV prehliada\u010Da
login.username = U\u017E\u00EDvate\u013Esk\u00E9 meno
login.password = Heslo
login.submit = Prihl\u00E1si\u0165 sa
login.sso.submit = Prihl\u00E1senie pomocou \u00FA\u010Dtu Tesco
login.invalidCredentials = Chybn\u00E9 prihlasovacie meno alebo heslo.
login.ssoError = Nemo\u017Eno sa prihl\u00E1si\u0165 pomocou \u00FA\u010Dtu Tesco.
login.loggedOut = Boli ste odhl\u00E1sen\u00FD.
login.sessionExpired = Platnos\u0165  V\u00E1\u0161ho prihl\u00E1senia vypr\u0161ala. Pros\u00EDm prihl\u00E1ste sa znovu.
menu.manualRedemption=Manu\u00E1lne zneplatnenie
# Menumenu.manualRedemption = Manu\u00E1lne uplatnenie
menu.transactions = Transakcia
menu.administration = Spr\u00E1va
menu.administration.users = U\u017E\u00EDvatelia
menu.app.version = Verzia
menu.batch = D\u00E1vka
menu.batch.batchFileDetail = Detail s\u00FAboru pouk\u00E1\u017Eok
menu.batch.batchFileSummary =  Sum\u00E1r pouk\u00E1\u017Eok
menu.reports = Reporty
menu.reports.xlsReports = XLS reporty

# Common DataTables labels
datatables.noData = D\u00E1ta nen\u00E1jden\u00E9
datatables.show = _START_ - _END_
datatables.show.empty=D\u00E1ta nen\u00E1jden\u00E9
datatables.lengthMenu=Zobrazi\u0165 _MENU_ riadkov
datatables.paginate.next = \u010Eal\u0161ie
datatables.paginate.previous = Predch\u00E1dzaj\u00FAce
datatables.processing = Vyh\u013Ead\u00E1vanie...

# Common UI component labels
common.filter = Filter
common.filter.search = Vyh\u013Eada\u0165
common.filter.reset = Vymaza\u0165 fitre
common.filter.ALL = V\u0161etko
common.filter.YES =  \u00C1NO
common.filter.NO = Nie
common.filter.dateFrom = OVV D\u00E1tum OD
common.filter.dateTo=OVV D\u00E1tum DO
common.filter.createdFrom = Vytvoren\u00E9 OD
common.filter.createdTo = Vytvoren\u00E9 DO
common.filter.xlsExport = Exportova\u0165 do Excelu
common.filter.expirationYear = Expir\u00E1cia
common.filter.trainingMode = \u0160koliaca transakcia

# Common errors
common.error.invalidRequest = Neplatn\u00E1 po\u017Eiadavka bola zamietnut\u00E1.

# Forms
form.field.error.required = Toto pole je vy\u017Eadovan\u00E9.
form.field.error.tooLong = T\u00E1to hodnota je pr\u00EDli\u0161 ve\u013Ek\u00E1.
form.field.error.lesser = T\u00E1to hodnota je v\u00E4\u010D\u0161ia ako {{fieldName}}.
form.field.error.invalidFormat = T\u00E1to hodnota je neplatn\u00E1.
form.number.error.notNumber = Hodnota mus\u00ED by\u0165 \u010D\u00EDslo.
form.number.error.invalid =  Nie je platn\u00E9 \u010D\u00EDslo.
form.number.error.gt = Hodnota mus\u00ED by\u0165 v\u00E4\u010D\u0161ia ako {{min}}.
form.number.error.loe = Hodnota mus\u00ED by\u0165 men\u0161ia alebo rovn\u00E1 {{max}}.
form.password.error.tooShort = Heslo mus\u00ED ma\u0165 d\u013A\u017Eku aspo\u0148 8 znakov.
form.password.error.digits = Heslo mus\u00ED obsahova\u0165 aspo\u0148 jednu \u010D\u00EDslicu.
form.password.error.lowercase = Heslo mus\u00ED obsahova\u0165 aspo\u0148 jedno mal\u00E9 p\u00EDsmeno.
form.password.error.uppercase = Heslo mus\u00ED obsahova\u0165 aspo\u0148 jedno ve\u013Ek\u00E9 p\u00EDsmeno.
form.password.error.match = Hesl\u00E1 sa nezhoduj\u00FA.
form.field.error.dateIsInPast = D\u00E1tum je u\u017E z uplynul\u00E9ho obdobia.
form.save = Ulo\u017Ei\u0165 zmeny
form.cancel = Zru\u0161i\u0165
form.send = Posla\u0165

# Messages comopnent
comp.messages.info = Inform\u00E1cie:
comp.messages.success = \u00DAspech:
comp.messages.warning = Upozornenie:
comp.messages.error = Chyba:

# Transaction List
transactions.list.heading = OVV Transakcia

# Administration - U\u017E\u00EDvatelia
administration.users.list.heading = U\u017E\u00EDvatelia
administration.users.list.addUser = Prida\u0165 u\u017E\u00EDvate\u013Ea
administration.users.editUser = Editova\u0165 u\u017E\u00EDvate\u013Ea
administration.users.changePassword = Zmeni\u0165  heslo
administration.users.disableUser = Zak\u00E1za\u0165 u\u017E\u00EDvate\u013Ea
administration.users.enableUser = Povoli\u0165 u\u017E\u00EDvate\u013Ea
administration.users.add.heading = Prida\u0165 u\u017E\u00EDvate\u013Ea
administration.users.add.title = Detail nov\u00E9ho u\u017E\u00EDvate\u013Ea
administration.users.edit.heading = Editova\u0165 u\u017E\u00EDvate\u013Ea
administration.users.edit.title = Detail nov\u00E9ho u\u017E\u00EDvate\u013Ea  {{username}}
administration.users.info.created = Nov\u00FD u\u017E\u00EDvate\u013E  bol \u00FAspe\u0161ne vytvoren\u00FD.
administration.users.info.updated = U\u017E\u00EDvate\u013E  bol \u00FAspe\u0161ne aktualizovan\u00FD.
administration.users.error.usernameTaken = U\u017E\u00EDvate\u013Esk\u00E9 meno u\u017E existuje, vlo\u017Ete in\u00E9.
administration.users.enableUser.success = U\u017E\u00EDvate\u013E {{username}} bol povolen\u00FD.
administration.users.disableUser.success = U\u017E\u00EDvate\u013E {{username}} bol zak\u00E1zan\u00FD.
administration.users.changePassword.success = Heslo pro u\u017E\u00EDvate\u013Ea {{username}} bolo \u00FAspe\u0161ne zmenen\u00E9.

# Transaction properties
transaction.serverDateTime=OVV D\u00E1tum, \u010Cas
transaction.deviceDateTime = POS D\u00E1tum, \u010Cas
transaction.countryCode = Krajina
transaction.deviceId = Poklad\u0148a
transaction.stan = \u010C\u00EDslo transakcie
transaction.paymentPlace = \u010C\u00EDslo obchodu
transaction.amount = \u010Ciastka
transaction.type = Typ transakcie
transaction.voucherNumber = S\u00E9riov\u00E9 \u010D\u00EDslo
transaction.ovvIssuer = Emitent
transaction.offlineMode = Offline
transaction.resultCode = V\u00FDsledok
transaction.store = Obchod
transaction.trainingMode = \u0160koliaca transakcia
transaction.id = ID transakcie
transaction.detail = Detail transakcie
transaction.validationHost = Overovate\u013E
transaction.partnerId = Partner
transaction.errorDescription = \u010Eal\u0161ie inform\u00E1cie o odpovedi
transaction.dateOrderInvalid = D\u00E1tumy od a do s\u00FA v nespr\u00E1vnom porad\u00ED
transaction.intervalToBig = Interval pre vyh\u013Ead\u00E1vanie m\u00F4\u017Ee by\u0165 maxim\u00E1lne {{interval}} dn\u00ED
transaction.intervalDateInFuture = D\u00E1tum nem\u00F4\u017Ee by\u0165 v bud\u00FAcnosti
transaction.manualRedemption = Ru\u010Dn\u00E9 uplatnenie

# User properties
user.username = U\u017E\u00EDvate\u013Esk\u00E9 meno
user.name = Meno
user.firstName = Krstn\u00E9 meno
user.lastName = Priezvisko
user.email = E-mail
user.role = Rola
user.active = Akt\u00EDvny
user.password = Heslo
user.passwordConfirm = Potvr\u010Fte heslo
user.store = Obchod
user.countryCode = Krajina

#
# Code lists
#

# Transaction Types
transaction.type.VALIDATION = Uplatnen\u00E9
transaction.type.CHECK = Uplatnen\u00E9
transaction.type.REVERSAL = Storno

# Result Codes
transaction.resultCode.APPROVED = Schv\u00E1len\u00FD
transaction.resultCode.ALREADY_USED = U\u017E pou\u017Eit\u00FD
transaction.resultCode.MESSAGE_FORMAT_ERROR = Nespr\u00E1vny form\u00E1t
transaction.resultCode.WRONG_REVERSAL = Chyba storna
transaction.resultCode.VOUCHER_NOT_FOUND = Nen\u00E1jden\u00FD
transaction.resultCode.ISSUER_NOT_FOUND = Nezn\u00E1my emitent
transaction.resultCode.TIMEOUT = \u010Casov\u00FD limit
transaction.resultCode.WRONG_REPEATED_REQUEST = Nespr\u00E1vne opakovan\u00E1 po\u017Eiadavka
transaction.resultCode.WRONG_VOUCHER_STATE = Chybn\u00FD stav pouk\u00E1\u017Eky
transaction.resultCode.WRONG_STORE = Chybn\u00FD obchod
transaction.resultCode.DECLINED=Blokovan\u00E9 emitentom
transaction.resultCode.CANNOT_PROCESS = Nie je mo\u017En\u00E9 spracova\u0165
transaction.resultCode.SCAN_AGAIN  = Opakovane naskenovan\u00FD
transaction.resultCode.STOLEN = Ukradnut\u00FD
transaction.resultCode.ALREADY_USED_SAME_TRN = U\u017E pou\u017Eit\u00FD v rovnakej transakcii
transaction.resultCode.AFTER_VALIDITY = Po expir\u00E1cii
transaction.resultCode.AMOUNT_DEF_NOT_FOUND=Suma nebola n\u00E1jden\u00E1
transaction.resultCode.NOT_ACCEPTED=Neakceptovan\u00FD

# User Roles
user.role.ADMIN = Administr\u00E1tor
user.role.VIEWER = Prehliada\u010D
user.role.MANUAL_REDEMPTION = Manu\u00E1lne uplatnenie
user.role.BATCH = D\u00E1vka
user.role.STORE_MANAGER = Manager

# Countries
country.PL = PL
country.CZ = CZ
country.SK = SK
country.HU = HU

#Manual Redemption
redemption.heading = Manu\u00E1lne vlo\u017Een\u00E1 pouk\u00E1\u017Eka
redemption.transactionType = Typ transakcie
redemption.voucherNumber = \u010C\u00EDslo pouk\u00E1\u017Eky
redemption.amount = \u010Ciastka
redemption.store = Obchod
redemption.device = Poklad\u0148a
redemption.stan = \u010C\u00EDslo transakcie
redemption.validation.APPROVED = Pouk\u00E1\u017Eka bola \u00FAspe\u0161ne schv\u00E1len\u00E1.
redemption.validation.ALREADY_USED = Pouk\u00E1\u017Eka bola u\u017E pou\u017Eit\u00E1.
redemption.validation.MESSAGE_FORMAT_ERROR = Nespr\u00E1vny form\u00E1t. Pros\u00EDm kontaktujte administr\u00E1tora.
redemption.validation.WRONG_REVERSAL = Pouk\u00E1\u017Eka nem\u00F4\u017Ee by\u0165 stornovan\u00E1.
redemption.validation.VOUCHER_NOT_FOUND = Pouk\u00E1\u017Eka nen\u00E1jden\u00E1.
redemption.validation.ISSUER_NOT_FOUND = Emitent pouk\u00E1\u017Eky nen\u00E1jden\u00FD.
redemption.validation.TIMEOUT = Vypr\u0161al \u010Dasov\u00FD limit. Pros\u00EDm sk\u00FAste znovu.
redemption.validation.WRONG_REPEATED_REQUEST = Nespr\u00E1vne opakovan\u00E1 po\u017Eiadavka. Pros\u00EDm kontaktujte administr\u00E1tora.
redemption.validation.WRONG_VOUCHER_STATE = Pouk\u00E1\u017Eka bola \u00FAspe\u0161ne schv\u00E1len\u00E1.
redemption.validation.WRONG_STORE = Bol vybran\u00FD nespr\u00E1vny obchod. Pros\u00EDm vyberte spr\u00E1vny obchod.
redemption.validation.DECLINED=Blokovan\u00E9 emitentom.
redemption.validation.CANNOT_PROCESS = Po\u017Eiadavku nemo\u017Eno spracova\u0165. Pros\u00EDm kontaktujte administr\u00E1tora.
redemption.validation.SCAN_AGAIN  = Opakovane naskenovan\u00FD.
redemption.validation.STOLEN = Ukradnut\u00FD.
redemption.validation.ALREADY_USED_SAME_TRN = U\u017E pou\u017Eit\u00FD v rovnakej transakcii.
redemption.validation.AFTER_VALIDITY = Po expir\u00E1cii.
redemption.validation.AMOUNT_DEF_NOT_FOUND = Hodnotu pouk\u00E1\u017Eky nemo\u017Eno ur\u010Di\u0165 automaticky.
redemption.validation.NOT_ACCEPTED=Neakceptovan\u00FD.
redemption.redemptionDateTime=D\u00E1tum
redemption.setAmount.heading=Nastavenie hodnoty pouk\u00E1\u017Eky
redemption.setAmount.info=Hodnotu pouk\u00E1\u017Eky nemo\u017Eno ur\u010Di\u0165 automaticky. Zadajte hodnotu manu\u00E1lne.
redemption.confirmAmount=Potvrdi\u0165

# Export
export.failed = Export nebol \u00FAspe\u0161n\u00FD.
export.tooBig = Exportovan\u00E9 d\u00E1ta s\u00FA pr\u00EDli\u0161 ve\u013Ek\u00E9, maxim\u00E1lny po\u010Det transakci\u00ED je 65 000. Pros\u00EDm zme\u0148te filter a sk\u00FAste znovu.
batch.detail.heading =  OVV Detail s\u00FAboru pouk\u00E1\u017Eok
batch.summary.heading = OVV Sum\u00E1r pouk\u00E1\u017Eok
batch.resultDescription = Popis v\u00FDsledku
batch.created = Vytvoren\u00E9
batch.fileName = Meno s\u00FAboru
batch.action = Akcia
batch.detail = Detail
batch.reconciliated = Reconciliated
batch.itemsCount = Po\u010Det polo\u017Eiek
batch.summary.detail = Detail d\u00E1vky
batch.resultCode.0 = Pouk\u00E1\u017Eka potvrden\u00E1 emitentom
batch.resultCode.1 = Pouk\u00E1\u017Eka nepotvrden\u00E1 emitentom,  emitent&rsquo;s je potrebn\u00E9 preveri\u0165. Emitent m\u00F4\u017Ee po\u017Eadova\u0165 poskytnutie pouk\u00E1\u017Eky spolo\u010Dnosti Tesco.
batch.resultCode.2 = Pouk\u00E1\u017Eka nebola ods\u00FAhlasen\u00E1 emitentom,  bolo potrebn\u00E9 extern\u00E9 (policajn\u00E9) preverenie.  Spolo\u010Dnos\u0165 Tesco je povinn\u00E1 poskytn\u00FA\u0165 t pouk\u00E1\u017Eku emitentovi.
batch.resultCode.3 = Pouk\u00E1\u017Eku odmietol  emitent, ktor\u00E1 u\u017E mala by\u0165 zru\u0161en\u00E1 in\u00FDm partnerom.
batch.resultCode.4 = Pouk\u00E1\u017Eka odmietnut\u00E1 emitentom \u2013 neexistuj\u00FAci alebo nespr\u00E1vny k\u00F3d.
batch.resultCode.5 = Pouk\u00E1\u017Eka nem\u00E1 platn\u00FA lokalitu. \u010C\u00EDslo obchodu  alebo/n\u00E1kladov\u00E9 centrum (alebo ch\u00FDba obchodu alebo / a n\u00E1kladov\u00E9 centrum). Proces kontroly p\u00F4vodu sa zastav\u00ED, pokia\u013E sa nevyrie\u0161ia ch\u00FDbaj\u00FAce polia.
batch.detail.title = Detail transakcie
batch.detail.close = Uzavrit
batch.detail.notExistTransaction = Inform\u00E1cie ID transakcie "{{id}}" zatia\u013E nie je k dispoz\u00EDcii, sk\u00FAste to nesk\u00F4r.
batch.costCentreSiteCode = N\u00E1kladov\u00E9 stredisko/\u010C\u00EDslo obchodu

# Change password
users.changePassword.heading = Zmena hesla
users.changePassword.title = Zadajte nov\u00E9 heslo pre u\u017E\u00EDvate\u013Ea {{username}}.
users.changePassword.oldPassword = P\u00F4vodn\u00E9 heslo
users.changePassword.newPassword = Nov\u00E9 heslo
users.changePassword.confirmNewPassword = Potvr\u010Fte nov\u00E9 heslo
changePassword.submit = Zmena hesla

oldPassword.empty.passwordChangeForm.oldPassword = Star\u00E9 heslo je pr\u00E1zdne.
newPassword.empty.passwordChangeForm.newPassword = Nov\u00E9 heslo je pr\u00E1zdne.
newPasswordConfirm.empty.passwordChangeForm.newPasswordConfirm = Potvrdenie nov\u00E9ho hesla je pr\u00E1zdne.
newPassword.noMatch.passwordChangeForm.newPassword = Nov\u00E9 hesl\u00E1 sa nezhoduj\u00FA.
newPassword.sameWithOld.passwordChangeForm.newPassword = Star\u00E9 a nov\u00E9 heslo je rovnak\u00E9.
password.tooWeak.passwordChangeForm = Nov\u00E9 heslo mus\u00ED ma\u0165 minim\u00E1lne 8 znakov (z toho aspo\u0148 jednu \u010D\u00EDslicu, jedno mal\u00E9 p\u00EDsmeno a jedno ve\u013Ek\u00E9 p\u00EDsmeno).
object.invalid.passwordChangeForm = Star\u00E9 heslo nie je spr\u00E1vne.

common.cancel = Zru\u0161i\u0165

transaction.category = Kateg\u00F3rie
transaction.category.MEAL = Stravn\u00FD
transaction.category.SOCIAL = Soci\u00E1lny
transaction.category.GIFT = Dar\u010Dekov\u00FD
transaction.category.SCHOOL = \u0160kolsk\u00FD
transaction.category.CHILDREN = Pre deti
transaction.category.OTHER = Ostatn\u00E9

# Reports
reports.list.heading = XLS Reporty
reports.list.createReport = Nov\u00FD report
reports.list.listTitle = Reporty
reports.list.downloadReport = Stiahnu\u0165 report
reports.list.createdAtHq = Vytvoren\u00E9 na HQ

reports.new.heading = Nov\u00FD report
reports.new.title = Vlastnosti nov\u00E9ho reportu
reports.new.parameters = Vstupn\u00E9 parametre
reports.new.info.success = Nov\u00FD report bol \u00FAspe\u0161ne vytvoren\u00FD a zaraden\u00FD na spracovanie.

# Report properties
report.type = Typ
report.status = Stav
report.store = Obchod
report.createdDate = D\u00E1tum vytvorenia
report.params.dateFrom = D\u00E1tum od
report.params.dateTo = D\u00E1tum do
report.params.store = Obchod

report.type.USED_VOUCHERS = Pou\u017Eit\u00E9 pouk\u00E1zky
report.type.CATEGORY=Preh\u013Ead kateg\u00F3ri\u00ED

# Report statuses
report.status.QUEUED = \u010Cak\u00E1 na spracovanie
report.status.IN_PROGRESS = Spracov\u00E1va sa
report.status.OK = Dokon\u010Den\u00FD
report.status.FAILED = Chyba
report.status.LARGE_EXPORT = Prekro\u010Den\u00FD limit


reports.dateInterval.invalid = D\u00E1tumy od a do s\u00FA v nespr\u00E1vnom porad\u00ED
reports.dateInterval.tooBig = Prekro\u010Den\u00FD interval pre vyh\u013Ead\u00E1vanie

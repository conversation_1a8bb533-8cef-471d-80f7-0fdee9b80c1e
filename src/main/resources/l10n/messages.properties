# Global application messages
app.title = Tesco OVV Transaction Viewer
app.name = OVV Transaction Viewer
app.fullName = OVV Viewer
app.shortName = OVV

# Navigation bar
navbar.myAccount = My Account
navbar.changePassword = Change Password
navbar.logout = Log Out

# Login page
login.title = OVV Transaction Viewer
login.header = Login to OVV Viewer
login.username = Username
login.password = Password
login.submit = Login
login.sso.submit = Login with Tesco account
login.invalidCredentials = Invalid username and/or password.
login.ssoError = Unable to login with Tesco account.
login.loggedOut = You have been logged out.
login.sessionExpired = Your session has expired. Please log in again.

# Menu
menu.manualRedemption = Manual Redemption
menu.transactions = Transactions
menu.administration = Administration
menu.administration.users = Users
menu.app.version = Version
menu.batch = Batch
menu.batch.batchFileDetail = Batch File Detail
menu.batch.batchFileSummary = Batch File Summary
menu.reports = Reports
menu.reports.xlsReports = XLS Reports

# Common DataTables labels
datatables.noData = No data found
datatables.show = _START_ - _END_
datatables.show.empty=No data found
datatables.lengthMenu = Show _MENU_ entries
datatables.paginate.next = Next
datatables.paginate.previous = Previous
datatables.processing = Searching...

# Common UI component labels
common.filter = Filter
common.filter.search = Search
common.filter.reset = Reset filters
common.filter.ALL = All
common.filter.YES = Yes
common.filter.NO = No
common.filter.dateFrom = OVV Date From
common.filter.dateTo = OVV Date To
common.filter.createdFrom = Created From
common.filter.createdTo = Created To
common.filter.xlsExport = Export to Excel
common.filter.expirationYear = Expiration
common.filter.trainingMode = Training Mode

# Common errors
common.error.invalidRequest = Invalid request rejected.

# Forms
form.field.error.required = This field is required.
form.field.error.tooLong = This value is too long.
form.field.error.lesser = This value has to be greater then {{fieldName}}.
form.field.error.invalidFormat = This value is invalid.
form.number.error.notNumber = The value must be a number.
form.number.error.invalid = Not a valid number.
form.number.error.gt = The value must be greater than {{min}}.
form.number.error.loe = The value must be less or equal than {{max}}.
form.password.error.tooShort = Password must be at least 8 characters in length.
form.password.error.digits = Password must contain at least 1 digit.
form.password.error.lowercase = Password must contain at least 1 lower case letter.
form.password.error.uppercase = Password must contain at least 1 upper case letter.
form.password.error.match = Passwords do not match.
form.field.error.dateIsInPast = Date is in past.
form.save = Save Changes
form.cancel = Cancel
form.send = Send

# Messages comopnent
comp.messages.info = Info:
comp.messages.success = Success:
comp.messages.warning = Warning:
comp.messages.error = Error:

# Transaction List
transactions.list.heading = OVV Transactions

# Administration - Users
administration.users.list.heading = Users
administration.users.list.addUser = Add User
administration.users.editUser = Edit User
administration.users.changePassword = Change Password
administration.users.disableUser = Disable User
administration.users.enableUser = Enable User
administration.users.add.heading = Add User
administration.users.add.title = Details of a new user
administration.users.edit.heading = Edit User
administration.users.edit.title = Details of user {{username}}
administration.users.info.created = New user has been successfully created.
administration.users.info.updated = User has been successfully updated.
administration.users.error.usernameTaken = The entered username is already used, please enter a different one.
administration.users.enableUser.success = User {{username}} has been enabled.
administration.users.disableUser.success = User {{username}} has been disabled.
administration.users.changePassword.success = Password for user {{username}} has been successfully changed.

# Transaction properties
transaction.serverDateTime = OVV Date, Time
transaction.deviceDateTime = POS Date, Time
transaction.countryCode = Country
transaction.deviceId = Device
transaction.stan = STAN
transaction.paymentPlace = Payment Place
transaction.amount = Amount
transaction.type = Type
transaction.voucherNumber = S/N
transaction.ovvIssuer = Issuer
transaction.offlineMode = Offline
transaction.trainingMode = Training Mode
transaction.resultCode = Result
transaction.store = Store
transaction.id = ID
transaction.detail = Detail
transaction.validationHost = Validation Host
transaction.partnerId = Partner
transaction.errorDescription = Additional Response Info
transaction.dateOrderInvalid = Date from is after date to
transaction.intervalToBig = Maximum interval for transaction search is {{interval}} days
transaction.intervalDateInFuture = Date cannot be in future
transaction.manualRedemption = Manual Redemption

# User properties
user.username = Username
user.name = Name
user.firstName = First Name
user.lastName = Last Name
user.email = E-mail
user.role = Role
user.active = Active
user.password = Password
user.passwordConfirm = Confirm password
user.store = Store
user.countryCode = Country
#
# Code lists
#

# Transaction Types
transaction.type.VALIDATION = Validation
transaction.type.CHECK = Validation
transaction.type.REVERSAL = Reversal

# Result Codes
transaction.resultCode.APPROVED = Approved
transaction.resultCode.ALREADY_USED = Already Used
transaction.resultCode.MESSAGE_FORMAT_ERROR = Format Error
transaction.resultCode.WRONG_REVERSAL = Wrong Reversal
transaction.resultCode.VOUCHER_NOT_FOUND = Not Found
transaction.resultCode.ISSUER_NOT_FOUND = Unknown Issuer
transaction.resultCode.TIMEOUT = Timeout
transaction.resultCode.WRONG_REPEATED_REQUEST = Wrong Repeated Request
transaction.resultCode.WRONG_VOUCHER_STATE = Wrong Voucher State
transaction.resultCode.WRONG_STORE = Wrong Store
transaction.resultCode.DECLINED=Blocked by isuuer
transaction.resultCode.CANNOT_PROCESS = Cannot Process
transaction.resultCode.SCAN_AGAIN  = Scan again
transaction.resultCode.STOLEN = Stolen
transaction.resultCode.ALREADY_USED_SAME_TRN = Already used same transaction
transaction.resultCode.AFTER_VALIDITY = After validity
transaction.resultCode.AMOUNT_DEF_NOT_FOUND=Amount not found
transaction.resultCode.NOT_ACCEPTED = Not accepted
# User Roles
user.role.ADMIN = Administrator
user.role.VIEWER = Viewer
user.role.MANUAL_REDEMPTION = Manual redemption
user.role.BATCH = Batch
user.role.STORE_MANAGER = Manager

# Countries
country.PL = PL
country.CZ = CZ
country.SK = SK
country.HU = HU

#Manual Redemption
redemption.heading = Manual voucher redemption
redemption.transactionType = Transaction type
redemption.voucherNumber = Voucher number
redemption.amount = Amount
redemption.store = Store
redemption.device = Device
redemption.stan = Stan
redemption.validation.APPROVED = Voucher was successfully approved.
redemption.validation.ALREADY_USED = Voucher is already used.
redemption.validation.MESSAGE_FORMAT_ERROR = Message format error. Please, contact you administrator.
redemption.validation.WRONG_REVERSAL = Voucher cannot be reversed.
redemption.validation.VOUCHER_NOT_FOUND = Voucher not found.
redemption.validation.ISSUER_NOT_FOUND = Issuer of voucher not found.
redemption.validation.TIMEOUT = Request timed out. Please, try again.
redemption.validation.WRONG_REPEATED_REQUEST = Wrong repeated request. Please, contact your administrator.
redemption.validation.WRONG_VOUCHER_STATE=Voucher was successfully approved.
redemption.validation.WRONG_STORE = Wrong store is selected. Please choose right store.
redemption.validation.DECLINED=Blocked by issuer.
redemption.validation.CANNOT_PROCESS = Request cannot be processed. Please, contact your administrator.
redemption.validation.SCAN_AGAIN  = Scan again
redemption.validation.STOLEN = Stolen
redemption.validation.ALREADY_USED_SAME_TRN = Already used same transaction.
redemption.validation.AFTER_VALIDITY = After validity.
redemption.validation.AMOUNT_DEF_NOT_FOUND = Voucher amount cannot be resolved automatically.
redemption.validation.NOT_ACCEPTED = Not accepted.
redemption.redemptionDateTime=Date
redemption.setAmount.heading=Setting voucher amount
redemption.setAmount.info=Voucher amount cannot be resolved automatically. Enter the amount manually.
redemption.confirmAmount=Confirm

# Export
export.failed = Export Failed
export.tooBig = Export data is too big, maximum number of transactions is 65000. Please change filters and try again.
batch.detail.heading = OVV Batch File Detail
batch.summary.heading = OVV Batch File Summary
batch.resultDescription = Result description
batch.created = Created
batch.fileName = File name
batch.action = Action
batch.detail = Detail
batch.reconciliated = Reconciliated
batch.itemsCount = Items Count
batch.summary.detail = Batch Detail
batch.resultCode.0 = a voucher is confirmed by the issuer
batch.resultCode.1 = a voucher was not confirmed by the issuer, issuer&rsquo;s internal investigation needed. The issuer may require the voucher to be provided by Tesco.
batch.resultCode.2 = a voucher was not confirmed by the issuer, an external (police, other public authority) investigation needed. Tesco is obliged to provide the voucher to a related issuer.
batch.resultCode.3 = a voucher was declined by the issuer due to be already invalidated by another partner
batch.resultCode.4 = a voucher was declined by the issuer due to be a fake (non-existing or wrong code)
batch.resultCode.5 = a voucher does not have a valid site Sitecode or/and Cost Centre (or missing Sitecode or/and Cost Centre). Process for checking originality will be stoped until resolved missing fields.

batch.detail.title = Transaction detail
batch.detail.close = Close
batch.detail.notExistTransaction = Information about transaction with id "{{id}}" not available yet, please try it later.
batch.costCentreSiteCode = Cost Centre/Site Code

# Change password
users.changePassword.heading = Change Password
users.changePassword.title = Insert new password for user {{username}}.
users.changePassword.oldPassword = Old Password
users.changePassword.newPassword = New Password
users.changePassword.confirmNewPassword = Confirm New Password
changePassword.submit = Change Password

oldPassword.empty.passwordChangeForm.oldPassword = Old password is empty.
newPassword.empty.passwordChangeForm.newPassword = New password is empty.
newPasswordConfirm.empty.passwordChangeForm.newPasswordConfirm = Confirmation of new password is empty.
newPassword.noMatch.passwordChangeForm.newPassword = New password do not match.
newPassword.sameWithOld.passwordChangeForm.newPassword = Old and new password are same.
password.tooWeak.passwordChangeForm = New password should have 8 chars (at least one digit, one upper case and one lower case char).
object.invalid.passwordChangeForm = Old password is not correct.

common.cancel = Cancel
common.submit = Submit

transaction.category = Category
transaction.category.MEAL = Meal
transaction.category.SOCIAL = Social
transaction.category.GIFT = Gift
transaction.category.SCHOOL = School
transaction.category.CHILDREN = Children
transaction.category.OTHER = Other

# Reports
reports.list.heading = XLS Reports
reports.list.createReport = New Report
reports.list.listTitle = Reports
reports.list.downloadReport = Download report
reports.list.createdAtHq = Created at HQ

reports.new.heading = New Report
reports.new.title = Properties of new report
reports.new.parameters = Input parameters
reports.new.info.success = New report has been successfully submitted for processing.

# Report properties
report.type = Type
report.status = Status
report.store = Store
report.createdDate = Date of creation
report.params.dateFrom = Date From
report.params.dateTo = Date To
report.params.store = Store

report.type.USED_VOUCHERS = Used Vouchers
report.type.CATEGORY = Category Summary

# Report statuses
report.status.QUEUED = Waiting for processing
report.status.IN_PROGRESS = In progress
report.status.OK = Finished
report.status.FAILED = Error
report.status.LARGE_EXPORT = Exceeded limit per report

reports.dateInterval.invalid = Date from is after date to
reports.dateInterval.tooBig = Exceeded maximum interval for report
# Global application messages
app.title = Tesco OVV Prohl\u00ED\u017Ee\u010D transakc\u00ED
app.name = OVV <PERSON>hl\u00ED\u017Ee\u010D transakc\u00ED
app.fullName = OVV Prohl\u00ED\u017Ee\u010D
app.shortName = OVV

# Navigation bar
navbar.myAccount = M\u016Fj \u00FA\u010Det
navbar.changePassword = Zm\u011Bnit heslo
navbar.logout = Odhl\u00E1sit se

# Login page
login.title = OVV Prohl\u00ED\u017Ee\u010D transakc\u00ED
login.header = P\u0159ihl\u00E1sit se do OVV Prohl\u00ED\u017Ee\u010De
login.username = U\u017Eivatelsk\u00E9 jm\u00E9no
login.password = Heslo
login.submit = P\u0159ihl\u00E1sit se
login.sso.submit = P\u0159ihl\u00E1\u0161en\u00ED pomoc\u00ED \u00FA\u010Dtu Tesco
login.invalidCredentials = Chybn\u00E9 p\u0159ihla\u0161ovac\u00ED jm\u00E9no a/nebo heslo.
login.ssoError = Nelze se p\u0159ihl\u00E1sit pomoc\u00ED \u00FA\u010Dtu Tesco.
login.loggedOut = Byli jste odhl\u00E1\u0161eni.
login.sessionExpired = Platnost Va\u0161eho p\u0159ihl\u00E1\u0161en\u00ED vypr\u0161ela. Pros\u00EDm p\u0159ihla\u0161te se znovu.

# Menu
menu.manualRedemption = Manu\u00E1ln\u00ED zneplatn\u011Bn\u00ED
menu.transactions = Transakce
menu.administration = Spr\u00E1va
menu.administration.users = U\u017Eivatel\u00E9
menu.app.version = Verze
menu.batch = D\u00E1vkov\u00E9 zpracov\u00E1n\u00ED
menu.batch.batchFileDetail = Obsah soubor\u016F
menu.batch.batchFileSummary =  P\u0159ehled soubor\u016F
menu.reports = Reporty
menu.reports.xlsReports = XLS reporty

# Common DataTables labels
datatables.noData = Data nenalezena
datatables.show = _START_ - _END_
datatables.show.empty=Data nenalezena
datatables.lengthMenu = Zobrazit _MENU_ \u0159\u00E1dk\u016F
datatables.paginate.next = Dal\u0161\u00ED
datatables.paginate.previous = P\u0159edchoz\u00ED
datatables.processing = Vyhled\u00E1v\u00E1n\u00ED...

# Common UI component labels
common.filter = Filtr
common.filter.search = Vyhledat
common.filter.reset = Vymazat filtry
common.filter.ALL = V\u0161e
common.filter.YES = ANO
common.filter.NO = NE
common.filter.dateFrom = OVV Datum OD
common.filter.dateTo = OVV Datum DO
common.filter.createdFrom = Vytvo\u0159eno OD
common.filter.createdTo = Vytvo\u0159eno DO
common.filter.xlsExport = Export do Excelu
common.filter.expirationYear = Expirace
common.filter.trainingMode = \u0160kol\u00EDc\u00ED transakce

# Common errors
common.error.invalidRequest = Neplatn\u00FD po\u017Eadavek byl zam\u00EDtnut.

# Forms
form.field.error.required = Toto pole je vy\u017Eadov\u00E1no.
form.field.error.tooLong = Tato hodnota je p\u0159\u00EDli\u0161 velk\u00E1.
form.field.error.lesser = Tato hodnota je v\u011Bt\u0161\u00ED ne\u017E {{fieldName}}.
form.field.error.invalidFormat = Tato hodnota je neplatn\u00E1.
form.number.error.notNumber = Hodnota mus\u00ED b\u00FDt \u010D\u00EDslo.
form.number.error.invalid = Nen\u00ED platn\u00E9 \u010D\u00EDslo.
form.number.error.gt = Hodnota mus\u00ED b\u00FDt v\u011Bt\u0161\u00ED ne\u017E {{min}}.
form.number.error.loe = Hodnota mus\u00ED b\u00FDt men\u0161\u00ED nebo rovna {{max}}.
form.password.error.tooShort = Heslo mus\u00ED b\u00FDt m\u00EDt d\u00E9lku alespo\u0148 8 znak\u016F.
form.password.error.digits = Heslo mus\u00ED obsahovat alespo\u0148 1 \u010D\u00EDslici.
form.password.error.lowercase = Heslo mus\u00ED obsahovat alespo\u0148 jedno mal\u00E9 p\u00EDsmeno.
form.password.error.uppercase = Heslo mus\u00ED obsahovat alespo\u0148 1 velk\u00E9 p\u00EDsmeno.
form.password.error.match = Hesla se neshoduj\u00ED.
form.field.error.dateIsInPast = Datum je z ji\u017E uplynul\u00E9ho obdob\u00ED.
form.save = Ulo\u017Eit zm\u011Bny
form.cancel = Zru\u0161it
form.send = Poslat

# Messages comopnent
comp.messages.info = Info:
comp.messages.success = \u00DAsp\u011Bch:
comp.messages.warning = Upozorn\u011Bn\u00ED:
comp.messages.error = Chyba:

# Transaction List
transactions.list.heading = OVV Transakce

# Administration - U\u017Eivatel\u00E9
administration.users.list.heading = U\u017Eivatel\u00E9
administration.users.list.addUser = P\u0159idat u\u017Eivatele
administration.users.editUser = Editovat u\u017Eivatele
administration.users.changePassword = Zm\u011Bnit heslo
administration.users.disableUser = Zak\u00E1zat u\u017Eivatele
administration.users.enableUser = Povolit u\u017Eivatele
administration.users.add.heading = P\u0159idat u\u017Eivatele
administration.users.add.title = Detail nov\u00E9ho u\u017Eivatele
administration.users.edit.heading = Editovat u\u017Eivatele
administration.users.edit.title = Detail nov\u00E9ho u\u017Eivatele  {{username}}
administration.users.info.created = Nov\u00FD u\u017Eivatel byl \u00FAsp\u011B\u0161n\u011B vytvo\u0159en.
administration.users.info.updated = U\u017Eivatel byl \u00FAsp\u011B\u0161n\u011B aktualizov\u00E1n.
administration.users.error.usernameTaken = U\u017Eivatelsk\u00E9 jm\u00E9no ji\u017E existuje, vlo\u017Ete jin\u00E9.
administration.users.enableUser.success = U\u017Eivatel {{username}} byl povolen.
administration.users.disableUser.success = U\u017Eivatel {{username}} byl zak\u00E1z\u00E1n.
administration.users.changePassword.success = Heslo pro u\u017Eivatele {{username}} bylo \u00FAsp\u011B\u0161n\u011B zm\u011Bn\u011Bno.

# Transaction properties
transaction.serverDateTime = OVV Datum, \u010Cas
transaction.deviceDateTime = POS Datum, \u010Cas
transaction.countryCode = Zem\u011B
transaction.deviceId = Pokladna
transaction.stan = \u010C\u00EDslo transakce
transaction.paymentPlace = \u010C\u00EDslo obchodu
transaction.amount = \u010C\u00E1stka
transaction.type = Typ transakce
transaction.voucherNumber = S\u00E9riov\u00E9 \u010D\u00EDslo
transaction.ovvIssuer = Emitent
transaction.offlineMode = Offline
transaction.resultCode = V\u00FDsledek
transaction.trainingMode = \u0160kol\u00EDc\u00ED transakce
transaction.store = Obchod
transaction.id = ID transakce
transaction.detail = Detail transakce
transaction.validationHost = Ov\u011B\u0159ovatel
transaction.partnerId = Partner
transaction.errorDescription = Dal\u0161\u00ED informace o odpov\u011Bdi
transaction.dateOrderInvalid = Datum od a do jsou v nespr\u00E1vn\u00E9m po\u0159ad\u00ED
transaction.intervalToBig = Interval pro vyhled\u00E1v\u00E1n\u00ED m\u016F\u017Ee b\u00FDt maxim\u00E1ln\u011B {{interval}} dn\u00ED
transaction.intervalDateInFuture = Datum nesm\u00ED b\u00FDt v budocnosti
transaction.manualRedemption = Ru\u010Dn\u00ED uplatn\u011Bn\u00ED

# User properties
user.username = U\u017Eivatelsk\u00E9 jm\u00E9no
user.name = Jm\u00E9no
user.firstName = K\u0159estn\u00ED jm\u00E9no
user.lastName = P\u0159\u00EDjmen\u00ED
user.email = E-mail
user.role = Role
user.active = Aktivn\u00ED
user.password = Heslo
user.passwordConfirm = Potvr\u010Fte heslo
user.store = Obchod
user.countryCode = Zem\u011B

#
# Code lists
#

# Transaction Types
transaction.type.VALIDATION = Uplatn\u011Bno
transaction.type.CHECK = Uplatn\u011Bno
transaction.type.REVERSAL = Storno

# Result Codes
transaction.resultCode.APPROVED = Schv\u00E1len\u00FD
transaction.resultCode.ALREADY_USED = Ji\u017E pou\u017Eit\u00FD
transaction.resultCode.MESSAGE_FORMAT_ERROR = Nespr\u00E1vn\u00FD  form\u00E1t
transaction.resultCode.WRONG_REVERSAL = Chyba storna
transaction.resultCode.VOUCHER_NOT_FOUND = Nenalezen\u00FD
transaction.resultCode.ISSUER_NOT_FOUND = Nezn\u00E1m\u00FD emitent
transaction.resultCode.TIMEOUT = \u010Casov\u00FD limit
transaction.resultCode.WRONG_REPEATED_REQUEST = Nespr\u00E1vn\u011B opakovan\u00FD po\u017Eadavek
transaction.resultCode.WRONG_VOUCHER_STATE = Chybn\u00FD stav pouk\u00E1zky
transaction.resultCode.WRONG_STORE = Chybn\u00FD obchod
transaction.resultCode.DECLINED=Blokovan\u00E9 emitentem
transaction.resultCode.CANNOT_PROCESS = Nelze zpracovat
transaction.resultCode.SCAN_AGAIN  = Opakovan\u011B naskenovan\u00FD
transaction.resultCode.STOLEN = Ukraden\u00FD
transaction.resultCode.ALREADY_USED_SAME_TRN = Ji\u017E pou\u017Eit\u00FD ve stejn\u00E9 transakci
transaction.resultCode.AFTER_VALIDITY = Po expiraci
transaction.resultCode.AMOUNT_DEF_NOT_FOUND=\u010C\u00E1stka nenalezena
transaction.resultCode.NOT_ACCEPTED=Nep\u0159ijat


# User Roles
user.role.ADMIN = Administr\u00E1tor
user.role.VIEWER = Prohl\u00ED\u017Ee\u010D
user.role.MANUAL_REDEMPTION = Manu\u00E1ln\u00ED zneplatn\u011Bn\u00ED
user.role.BATCH = D\u00E1vka
user.role.STORE_MANAGER = Manager

# Countries
country.PL = PL
country.CZ = CZ
country.SK = SK
country.HU = HU

#Manual Redemption
redemption.heading = Manu\u00E1ln\u00ED vlo\u017Een\u00ED pouk\u00E1zky
redemption.transactionType = Typ transakce
redemption.voucherNumber = \u010C\u00EDslo pouk\u00E1zky
redemption.amount = \u010C\u00E1stka
redemption.store = Obchod
redemption.device = Pokladna
redemption.stan = \u010C\u00EDslo transakce
redemption.validation.APPROVED = Pouk\u00E1zka byla \u00FAsp\u011B\u0161n\u011B schv\u00E1lena.
redemption.validation.ALREADY_USED = Pouk\u00E1zka ji\u017E byla pou\u017Eita.
redemption.validation.MESSAGE_FORMAT_ERROR = Nespr\u00E1vn\u00FD form\u00E1t. Pros\u00EDm kontaktujte administr\u00E1tora.
redemption.validation.WRONG_REVERSAL = Pouk\u00E1zka nem\u016F\u017Ee b\u00FDt stornov\u00E1na.
redemption.validation.VOUCHER_NOT_FOUND = Pouk\u00E1zka nenalezena.
redemption.validation.ISSUER_NOT_FOUND = Emitent pouk\u00E1zky nenalezen.
redemption.validation.TIMEOUT = Vypr\u0161el \u010Dasov\u00FD limit. Pros\u00EDm zkuste znovu.
redemption.validation.WRONG_REPEATED_REQUEST = Nespr\u00E1vn\u011B opakovan\u00FD po\u017Eadavek. Pros\u00EDm kontaktujte administr\u00E1tora.
redemption.validation.WRONG_VOUCHER_STATE=Pouk\u00E1zka byla \u00FAsp\u011B\u0161n\u011B schv\u00E1lena.
redemption.validation.WRONG_STORE = Byl vybr\u00E1n nespr\u00E1vn\u00FD obchod. Pros\u00EDm vyberte spr\u00E1vn\u00FD obchod.
redemption.validation.DECLINED=Blokovan\u00E9 emitentem.
redemption.validation.CANNOT_PROCESS = Po\u017Eadavek nelze zpracovat. Pros\u00EDm kontaktujte administr\u00E1tora.
redemption.validation.SCAN_AGAIN  = Opakovan\u011B naskenovan\u00FD
redemption.validation.STOLEN = Ukraden\u00FD
redemption.validation.ALREADY_USED_SAME_TRN = Ji\u017E pou\u017Eit\u00FD ve stejn\u00E9 transakci.
redemption.validation.AFTER_VALIDITY = Po expiraci.
redemption.validation.AMOUNT_DEF_NOT_FOUND = Hodnotu pouk\u00E1zky nelze ur\u010Dit automaticky.
redemption.validation.NOT_ACCEPTED=Nep\u0159ijat.
redemption.redemptionDateTime=Datum
redemption.setAmount.heading=Nastaven\u00ED hodnoty pouk\u00E1zky
redemption.setAmount.info=Hodnotu pouk\u00E1zky nelze ur\u010Dit automaticky. Zadejte hodnotu manu\u00E1ln\u011B.
redemption.confirmAmount=Potvrdit

# Export
export.failed = Export selhal
export.tooBig = Exportovan\u00E1 data jsou p\u0159\u00EDli\u0161 velk\u00E1, maxim\u00E1ln\u00ED po\u010Det transakc\u00ED je 65000. Pros\u00EDm zm\u011B\u0148te filtry a zkuste znovu.
batch.detail.heading =  Obsah soubor\u016F s pouk\u00E1zkami
batch.summary.heading = P\u0159ehled soubor\u016F s pouk\u00E1zkami
batch.resultDescription = Popis v\u00FDsledku
batch.created = Vytvo\u0159eno
batch.fileName = Jm\u00E9no souboru
batch.action = Akce
batch.detail = Detail
batch.reconciliated = Zpr\u00E1cov\u00E1no
batch.itemsCount = Po\u010Det polo\u017Eek
batch.summary.detail = Obsah souboru
batch.resultCode.0 = Pouk\u00E1zka potvrzena emitentem
batch.resultCode.1 = Pouk\u00E1zka nepotvrzena eminentem, emitent&rsquo;s je zapot\u0159eb\u00ED pro\u0161et\u0159it. Emitent m\u016F\u017Ee po\u017Eadovat poskytnut\u00ED pouk\u00E1zky spole\u010Dnost\u00ED Tesco.
batch.resultCode.2 = Pouk\u00E1zka nebyla odsouhlasena emitentem, bylo zapot\u0159eb\u00ED extern\u00ED (policejn\u00ED) pro\u0161et\u0159en\u00ED.  Spole\u010Dnost Tesco je povinna poskytnout pouk\u00E1zku emitentovi.
batch.resultCode.3 = Pouk\u00E1zku odm\u00EDtl emitent, kter\u00FD ji\u017E m\u011Bl b\u00FDt zru\u0161en jin\u00FDm partnerem.
batch.resultCode.4 = Pouk\u00E1zka odm\u00EDtnuta emitentem - neexistuj\u00EDc\u00ED nebo nespr\u00E1vn\u00FD k\u00F3d.
batch.resultCode.5 = Pouk\u00E1zka nem\u00E1 platnou lokalitu \u010C\u00EDslo obchodu nebo / a n\u00E1kladov\u00E9 centrum (nebo chyb\u00ED obchodu nebo / a n\u00E1kladov\u00E9 centrum). Proces kontroly p\u016Fvodu se zastav\u00ED, dokud se nevy\u0159e\u0161\u00ED chyb\u011Bj\u00EDc\u00ED pole.
batch.detail.title = Detail transakce
batch.detail.close = Zav\u0159\u00EDt
batch.detail.notExistTransaction = Informace ID transakce "{{id}}" zat\u00EDm nen\u00ED k dispozici, zkuste to pozd\u011Bji.
batch.costCentreSiteCode = N\u00E1kladov\u00E9 st\u0159edisko/\u010C\u00EDslo obchodu

# Change password
users.changePassword.heading = Zm\u011Bna hesla
users.changePassword.title = Zadejte nov\u00E9 heslo pro u\u017Eivatele {{username}}.
users.changePassword.oldPassword = P\u016Fvodn\u00ED heslo
users.changePassword.newPassword = Nov\u00E9 heslo
users.changePassword.confirmNewPassword = Potvr\u010Fte nov\u00E9 heslo
changePassword.submit = Zm\u011Bna hesla

oldPassword.empty.passwordChangeForm.oldPassword = Star\u00E9 heslo je pr\u00E1zdn\u00E9.
newPassword.empty.passwordChangeForm.newPassword = Nov\u00E9 heslo je pr\u00E1zdn\u00E9.
newPasswordConfirm.empty.passwordChangeForm.newPasswordConfirm = Potvrzen\u00ED nov\u00E9ho hesla je pr\u00E1zdn\u00E9.
newPassword.noMatch.passwordChangeForm.newPassword = Nov\u00E1 hesla si neodpov\u00EDdj\u00ED.
newPassword.sameWithOld.passwordChangeForm.newPassword = Star\u00E9 a nov\u00E9 heslo jsou stejn\u00E9.
password.tooWeak.passwordChangeForm = Nov\u00E9 heslo mus\u00ED m\u00EDt nejm\u00E9n\u011B 8 znak\u016F (z toho aspo\u0148 jednu \u010D\u00EDslici, jedno mal\u00E9 p\u00EDsmeno a jedno velk\u00E9 p\u00EDsmeno).
object.invalid.passwordChangeForm = Star\u00E9 heslo nen\u00ED spr\u00E1vn\u00E9.


common.cancel = Zru\u0161it

transaction.category = Kategorie
transaction.category.MEAL = J\u00EDdeln\u00ED
transaction.category.SOCIAL = Soci\u00E1ln\u00ED
transaction.category.GIFT = D\u00E1rkov\u00FD
transaction.category.SCHOOL = \u0160koln\u00ED
transaction.category.CHILDREN = D\u011Btsk\u00FD
transaction.category.OTHER = Ostatn\u00ED


# Reports
reports.list.heading = XLS Reporty
reports.list.createReport = Nov\u00FD report
reports.list.listTitle = Reporty
reports.list.downloadReport = St\u00E1hnout report

reports.new.heading = Nov\u00FD report
reports.new.title = Vlastnosti nov\u00E9ho reportu
reports.new.parameters = Vstupn\u00ED parametry
reports.new.info.success = Nov\u00FD report byl \u00FAsp\u011B\u0161n\u011B vytvo\u0159en a za\u0159azen ke zpracov\u00E1n\u00ED.

# Report properties
report.type = Typ
report.status = Stav
report.store = Prodejna
report.createdDate = Datum vytvo\u0159en\u00ED
report.params.dateFrom = Datum od
report.params.dateTo = Datum do
report.params.store = Prodejna

report.type.USED_VOUCHERS = Pou\u017Eit\u00E9 pouk\u00E1zky
report.type.CATEGORY = P\u0159ehled kategori\u00ED

# Report statuses
report.status.QUEUED = \u010Cek\u00E1 na zpracov\u00E1n\u00ED
report.status.IN_PROGRESS = Zpracov\u00E1v\u00E1 se
report.status.OK = Dokon\u010Den\u00FD
report.status.FAILED = Chyba
report.status.LARGE_EXPORT = P\u0159ekro\u010Den limit pro export

reports.dateInterval.invalid = Datum od a do jsou v nespr\u00E1vn\u00E9m po\u0159ad\u00ED
reports.dateInterval.tooBig = P\u0159ekro\u010Den maxim\u00E1ln\u00ED interval pro report
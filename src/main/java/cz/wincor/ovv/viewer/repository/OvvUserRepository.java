package cz.wincor.ovv.viewer.repository;

import cz.wincor.ovv.viewer.model.entity.OvvUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;


/**
 * Repository for {@link OvvUser} entity.
 *
 * <AUTHOR>
 */
public interface OvvUserRepository extends JpaRepository<OvvUser, Long>,
        QuerydslPredicateExecutor<OvvUser> {

    /**
     * Find a user by username.
     * @param username Username to match.
     * @return Existing {@link OvvUser} or {@code null} if no user with the given username is registered.
     */
    OvvUser findByUsername(String username);
}

package cz.wincor.ovv.viewer.model;

import cz.wincor.ovv.viewer.dto.DtoMapper;
import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.entity.Store;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.oidc.OidcIdToken;
import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

public class TescoOidcUser implements DtoMapper<UserDto>, OidcUser, User {
    private final OidcUser delegated;

    private final String employeeNumber;

    private final String preferredUsername;

    private final Collection<UserRole> roles;

    private final Collection<CountryEnum> countries;

    private final UserBusinessCategory businessCategory;

    private final String division;

    public TescoOidcUser(OidcUser delegated, String employeeNumber,
                         String preferredUsername, Collection<UserRole> roles, Collection<CountryEnum> countries,
                         UserBusinessCategory businessCategory, String division) {
        this.delegated = delegated;
        this.employeeNumber = employeeNumber;
        this.preferredUsername = preferredUsername;
        this.roles = roles;
        this.countries = countries;
        this.businessCategory = businessCategory;
        this.division = division;
    }

    /**
     * Create {@link UserDto} object from this user
     *
     * @return
     */
    @Override
    public UserDto mapToDto() {
        UserDto userDTO = new UserDto();
        userDTO.setActive(true);
        userDTO.setRoles(roles);
        userDTO.setCountries(countries);
        return userDTO;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return roles.stream()
                .map(role -> new SimpleGrantedAuthority("ROLE_" + role))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getClaims() {
        return delegated.getClaims();
    }

    @Override
    public OidcUserInfo getUserInfo() {
        return delegated.getUserInfo();
    }

    @Override
    public OidcIdToken getIdToken() {
        return delegated.getIdToken();
    }

    @Override
    public Map<String, Object> getAttributes() {
        return delegated.getAttributes();
    }

    @Override
    public String getName() {
        return delegated.getName();
    }

    public String getEmployeeNumber() {
        return employeeNumber;
    }

    @Override
    public String getUsername() {
        return preferredUsername;
    }

    @Override
    public Collection<UserRole> getRoles() {
        return roles;
    }

    @Override
    public Collection<CountryEnum> getCountries() {
        return countries;
    }

    public UserBusinessCategory getBusinessCategory() {
        return businessCategory;
    }

    public String getDivision() {
        return division;
    }

    @Override
    public boolean canAccessStore(Store store) {
        if (!getRoles().contains(UserRole.ADMIN)) {
            return switch (getBusinessCategory()) {
                case STORE -> checkStoreUserStoreAccess(store);
                case OFFICE -> checkOfficeUserStoreAccess(store);
            };
        }
        return true;
    }

    @Override
    public String getOvvRequestOperator() {
        return getEmployeeNumber();
    }

    @Override
    public String getLoggerIdentifier() {
        return getEmployeeNumber();
    }

    private boolean checkOfficeUserStoreAccess(Store store) {
        return getCountries().contains(store.getCountryCode());
    }

    private boolean checkStoreUserStoreAccess(Store store) {
        return getDivision().equals(store.getSiteCode());
    }
}

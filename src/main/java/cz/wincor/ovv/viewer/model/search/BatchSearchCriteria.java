package cz.wincor.ovv.viewer.model.search;

import cz.wincor.ovv.viewer.model.entity.ReportBatch;
import cz.wincor.ovv.viewer.xsd.CountryCode;

import java.time.LocalDateTime;

import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * Criteria parameters used for searching {@link ReportBatch} entities.
 *
 * <AUTHOR>
 */
public class BatchSearchCriteria extends BaseSearchCriteria {

    private String[] issuer;
    private Boolean reconciliated;
    private String voucherNumber;
    private String siteCode;
    private Integer resultCode;
    private String fileName;
    private LocalDateTime sentToIssuerFrom;
    private LocalDateTime sentToIssuerTo;


    //~ Plain getters & setters
    public String getVoucherNumber() {
        return voucherNumber;
    }
    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    public String[] getIssuer() {
        return issuer;
    }
    public void setIssuer(String[] issuer) {
        this.issuer = issuer;
    }

    public Integer getResultCode() {
        return resultCode;
    }
    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public String getSiteCode() {
        return siteCode;
    }
    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public Boolean getReconciliated() {
        return reconciliated;
    }

    public void setReconciliated(Boolean reconciliated) {
        this.reconciliated = reconciliated;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDateTime getSentToIssuerFrom() {
        return sentToIssuerFrom;
    }

    public void setSentToIssuerFrom(LocalDateTime sentToIssuerFrom) {
        this.sentToIssuerFrom = sentToIssuerFrom;
    }

    public LocalDateTime getSentToIssuerTo() {
        return sentToIssuerTo;
    }

    public void setSentToIssuerTo(LocalDateTime sentToIssuerTo) {
        this.sentToIssuerTo = sentToIssuerTo;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("issuer", issuer)
                .append("reconciliated", reconciliated)
                .append("voucherNumber", voucherNumber)
                .append("siteCode", siteCode)
                .append("resultCode", resultCode)
                .append("fileName", fileName)
                .append("sentToIssuerFrom", sentToIssuerFrom)
                .append("sentToIssuerTo", sentToIssuerTo)
                .append("page", getPage())
                .append("size", getSize())
                .append("sortBy", getSortBy())
                .append("direction", getDirection())
                .append("storeIds", getStoreIds())
                .toString();
    }
}

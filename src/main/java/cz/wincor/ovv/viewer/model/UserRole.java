package cz.wincor.ovv.viewer.model;

public enum UserRole {
    /**
     * Administrator user with access to administration menu.
     */
    ADMIN,
    /**
     * Viewer user with read-only access.
     */
    VIEWER,
    /**
     * Manual redemption user
     */
    MANUAL_REDEMPTION,
    /**
     * batch user
     */
    BATCH,
    /**
     * Store manager
     */
    STORE_MANAGER;

    public static UserRole fromOidcClaimRole(String role) {
        switch (role.toLowerCase()) {
            case "administrator":
                return UserRole.ADMIN;
            case "viewer":
                return UserRole.VIEWER;
            case "manualredeption":
                return UserRole.MANUAL_REDEMPTION;
            case "batch":
                return UserRole.BATCH;
            case "manager":
                return UserRole.STORE_MANAGER;
            default:
                throw new IllegalArgumentException("Unknown user role '" + role + "'.");
        }
    }
}

package cz.wincor.ovv.viewer.model.entity;


import cz.wincor.ovv.viewer.dto.BasicEntityDTO;
import cz.wincor.ovv.viewer.dto.DtoMapper;
import cz.wincor.ovv.viewer.dto.StoreDto;
import cz.wincor.ovv.viewer.enums.CountryEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.util.Objects;

/**
 * Store entity.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "ovv_store")
public class Store implements DtoMapper<StoreDto> {

    @Id
    private Long id;

    @Column(name = "cost_centre")
    private String costCentre;

    @Column(name = "country_code", length = 2, unique = true)
    @Enumerated(EnumType.STRING)
    private CountryEnum countryCode;

    @Column(name = "name")
    private String name;

    @Column(name = "site_code", unique = true)
    private String siteCode;

    @Column(name = "partner_id", unique = true)
    private String partnerId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCostCentre() {
        return costCentre;
    }

    public void setCostCentre(String costCentre) {
        this.costCentre = costCentre;
    }

    public CountryEnum getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(CountryEnum countryCode) {
        this.countryCode = countryCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSiteCode() {
        if (siteCode.length() == 4) {
            return countryCode.getDivisionPrefix() + siteCode;
        }
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getPartnerId() {
        return partnerId;
    }



    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("Store [id=");
        builder.append(id);
        builder.append(", name=");
        builder.append(name);
        builder.append(", siteCode=");
        builder.append(siteCode);
        builder.append(", partnerId=");
        builder.append(partnerId);
        builder.append("]");
        return builder.toString();
    }

    @Override
    public StoreDto mapToDto() {
        StoreDto storeDto = new StoreDto();
        storeDto.setCostCentre(costCentre);
        storeDto.setCountryCode(countryCode);
        storeDto.setId(id);
        storeDto.setName(name);
        storeDto.setPartnerId(partnerId);
        storeDto.setSiteCode(siteCode);
        return storeDto;
    }

    public BasicEntityDTO mapToBasicDto() {
        return new BasicEntityDTO(id, name);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Store)) return false;
        Store store = (Store) o;
        return Objects.equals(getId(), store.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }
}

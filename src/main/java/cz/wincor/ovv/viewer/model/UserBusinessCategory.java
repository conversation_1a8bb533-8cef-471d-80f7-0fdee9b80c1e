package cz.wincor.ovv.viewer.model;

public enum UserBusinessCategory {

    STORE,
    OFFICE;

    public static UserBusinessCategory fromOidcClaim(String value) {
        switch (value.toLowerCase()) {
            case "store":
                return UserBusinessCategory.STORE;
            case "office":
                return UserBusinessCategory.OFFICE;
            default:
                throw new IllegalArgumentException("Unknown user business category '" + value + "'.");
        }
    }
}

package cz.wincor.ovv.viewer.model;

import cz.wincor.ovv.viewer.dto.DtoMapper;
import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.entity.Store;

import java.util.Collection;

public interface User extends DtoMapper<UserDto> {

    boolean canAccessStore(Store store);

    Collection<UserRole> getRoles();

    Collection<CountryEnum> getCountries();

    String getUsername();

    String getOvvRequestOperator();

    String getLoggerIdentifier();
}

package cz.wincor.ovv.viewer.model.search;

import java.util.LinkedList;
import java.util.List;

import cz.wincor.ovv.viewer.xsd.CountryCode;
import org.springframework.data.domain.Sort;

/**
 * Base search criteria common for most searches.
 *
 * <AUTHOR>
 */
public class BaseSearchCriteria {

    // Paging parameters
    private Integer page;
    private Integer size;

    // Common search criteria
    private List<CountryCode> countries;
    private List<Long> storeIds;
    private List<String> siteCodes;

    // Sorting parameters
    private List<String> sortBy = new LinkedList<>();
    private Sort.Direction direction;

    /**
     * Append a new sort column to existing sorting (if any exists)
     * @param sort
     */
    public void addSorting(String sort) {
        sortBy.add(sort);
    }

    //~ Plain getters & setters

    public Integer getPage() {
        return page;
    }
    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }
    public void setSize(Integer size) {
        this.size = size;
    }

    public List<String> getSortBy() {
        return sortBy;
    }
    public void setSortBy(List<String> sortBy) {
        this.sortBy = sortBy;
    }

    public Sort.Direction getDirection() {
        return direction;
    }
    public void setDirection(Sort.Direction direction) {
        this.direction = direction;
    }

    public List<CountryCode> getCountries() {
        return countries;
    }
    public void setCountries(List<CountryCode> countries) {
        this.countries = countries;
    }

    public List<Long> getStoreIds() {
        return storeIds;
    }
    public void setStoreIds(List<Long> storeIds) {
        this.storeIds = storeIds;
    }

    public List<String> getSiteCodes() {
        return siteCodes;
    }
    public void setSiteCodes(List<String> siteCodes) {
        this.siteCodes = siteCodes;
    }
}

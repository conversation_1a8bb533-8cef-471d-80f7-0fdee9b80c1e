package cz.wincor.ovv.viewer.model.entity;

import cz.wincor.ovv.viewer.dto.DtoMapper;
import cz.wincor.ovv.viewer.dto.ReportDTO;
import cz.wincor.ovv.viewer.model.ReportStatus;
import cz.wincor.ovv.viewer.model.ReportType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Report entity.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "REPORT")
@EntityListeners(AuditingEntityListener.class)
public class Report implements DtoMapper<ReportDTO>, Serializable {

    private static final long serialVersionUID = -118158848970530872L;

    /**
     * Unique artificial ID of the report.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Type of the report.
     */
    @Column(name = "type", length = 32, nullable = false)
    @Enumerated(EnumType.STRING)
    private ReportType type;

    /**
     * Status of the report.
     */
    @Column(name = "status", length = 16, nullable = false)
    @Enumerated(EnumType.STRING)
    private ReportStatus status;

    /**
     * Store owning the report.
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = true)
    @JoinColumn(name = "store_id")
    private Store store;

    /**
     * Serialized report input parameters.
     */
    @Column(name = "params", length = 255, nullable = true)
    private String params;

    /**
     * Report content - binary data.
     */
    @Column(name = "data", nullable = true)
    @Lob
    private byte[] data;

    /**
     * Audit field - date of creation.
     */
    @CreatedDate
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    /**
     * Audit field - date of last modification.
     */
    @LastModifiedDate
    @Column(name = "modified_date", nullable = false)
    private LocalDateTime modifiedDate;

    /**
     * Audit field - DB (legacy) user who created the report.
     * No longer used as user can also be signed from Tesco OIDC.
     * Kept for backwards compatibility.
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by_user_id")
    private OvvUser createdByLegacy;

    /**
     * Audit field - user who created the report.
     */
    @Column(name = "created_by_user", nullable = true)
    private String createdBy;

    @Override
    public ReportDTO mapToDto() {
        ReportDTO dto = new ReportDTO();
        dto.setId(id);
        dto.setType(type);
        dto.setStatus(status);
        if (store != null) {
            dto.setStore(store.mapToDto());
        }
        dto.setParams(params);
        dto.setData(data);
        dto.setCreatedAt(createdDate);
        if (ReportStatus.OK == status || ReportStatus.FAILED == status) {
            dto.setFinishedAt(modifiedDate);
        }
        if (createdBy != null) {
            dto.setCreatedBy(createdBy);
        } else if (createdByLegacy != null) {
            dto.setCreatedBy(createdByLegacy.getUsername());
        }
        return dto;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE)
                .append("id", id)
                .append("type", type)
                .append("status", status)
                .toString();
    }

    //~ Plain getters & setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ReportType getType() {
        return type;
    }

    public void setType(ReportType type) {
        this.type = type;
    }

    public ReportStatus getStatus() {
        return status;
    }

    public void setStatus(ReportStatus status) {
        this.status = status;
    }

    public Store getStore() {
        return store;
    }

    public void setStore(Store store) {
        this.store = store;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(LocalDateTime modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
}

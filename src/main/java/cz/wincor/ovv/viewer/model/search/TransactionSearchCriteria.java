package cz.wincor.ovv.viewer.model.search;

import java.time.LocalDateTime;
import java.time.Year;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.builder.ToStringBuilder;

import cz.wincor.ovv.viewer.model.ValidationHost;
import cz.wincor.ovv.viewer.model.entity.Transaction;
import cz.wincor.ovv.viewer.xsd.CountryCode;
import cz.wincor.ovv.viewer.xsd.ResultCode;
import cz.wincor.ovv.viewer.xsd.TransType;

/**
 * Criteria parameters used for searching {@link Transaction} entities.
 *
 * <AUTHOR>
 */
public class TransactionSearchCriteria extends BaseSearchCriteria {

    private LocalDateTime dateTimeFrom;
    private LocalDateTime dateTimeTo;
    private String deviceId;
    private String siteCode;
    private Integer stan;
    private TransType type;
    private String voucherNumber;
    private String[] issuer;
    private ValidationHost validationHost;
    private String[] resultCode;
    private Year expirationYear;
    private Boolean trainingMode;
    private Boolean offlineMode;
    private Boolean manualRedemption;


    //~ Plain getters & setters


    public LocalDateTime getDateTimeFrom() {
        return dateTimeFrom;
    }
    public void setDateTimeFrom(LocalDateTime dateTimeFrom) {
        this.dateTimeFrom = dateTimeFrom;
    }

    public LocalDateTime getDateTimeTo() {
        return dateTimeTo;
    }
    public void setDateTimeTo(LocalDateTime dateTimeTo) {
        this.dateTimeTo = dateTimeTo;
    }

    public String getDeviceId() {
        return deviceId;
    }
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getStan() {
        return stan;
    }
    public void setStan(Integer stan) {
        this.stan = stan;
    }

    public TransType getType() {
        return type;
    }
    public void setType(TransType type) {
        this.type = type;
    }

    public String getVoucherNumber() {
        return voucherNumber;
    }
    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    public String[] getIssuer() {
        return issuer;
    }
    public void setIssuer(String[] issuer) {
        this.issuer = issuer;
    }

    public ValidationHost getValidationHost() {
        return validationHost;
    }
    public void setValidationHost(ValidationHost validationHost) {
        this.validationHost = validationHost;
    }

    public String[] getResultCode() {
        return resultCode;
    }
    public void setResultCode(String[] resultCode) {
        this.resultCode = resultCode;
    }

    public Year getExpirationYear() {
        return expirationYear;
    }
    public void setExpirationYear(Year year) {
        this.expirationYear = year;
    }

    public Boolean getTrainingMode() {
        return trainingMode;
    }
    public void setTrainingMode(Boolean trainingMode) {
        this.trainingMode = trainingMode;
    }

    public Boolean getOfflineMode() {
        return offlineMode;
    }
    public void setOfflineMode(Boolean offlineMode) {
        this.offlineMode = offlineMode;
    }

    public Boolean getManualRedemption() {
        return manualRedemption;
    }
    public void setManualRedemption(Boolean manualRedemption) {
        this.manualRedemption = manualRedemption;
    }

    public String getSiteCode() {
        return siteCode;
    }
    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }
}

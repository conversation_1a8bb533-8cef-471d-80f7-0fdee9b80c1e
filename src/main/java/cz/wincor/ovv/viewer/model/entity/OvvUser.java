package cz.wincor.ovv.viewer.model.entity;

import cz.wincor.ovv.viewer.dto.DtoMapper;
import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.User;
import cz.wincor.ovv.viewer.model.UserRole;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Entity
@Table(name = "ovv_user")
public class OvvUser extends BaseAuditedEntity implements DtoMapper<UserDto>, UserDetails, User {

    private static final long serialVersionUID = 4603020647203663564L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "username", length = 64, unique = true, nullable = false)
    private String username;

    @Column(name = "first_name", length = 64)
    private String firstName;

    @Column(name = "last_name", length = 64)
    private String lastName;

    @Column(name = "email", length = 128)
    private String email;

    @Column(name = "role", length = 16, nullable = false)
    @Enumerated(EnumType.STRING)
    private UserRole role;

    @Column(name = "password", length = 64, nullable = false)
    private String password;

    @Column(name = "active", nullable = false)
    private boolean active = true;

    @Column(name = "is_account_non_expired", nullable = false)
    private boolean isAccountNonExpired = true;

    @Column(name = "country_code")
    @Enumerated(EnumType.STRING)
    private CountryEnum countryCode;

    @ManyToMany(fetch= FetchType.EAGER)
    @JoinTable(
            name="ovv_user_store",
            joinColumns=@JoinColumn(name="user_id", referencedColumnName="id"),
            inverseJoinColumns=@JoinColumn(name="store_id", referencedColumnName="id"))
    private Collection<Store> stores;

    /**
     * Create {@link UserDto} object from this user
     * @return
     */
    @Override
    public UserDto mapToDto() {
        cz.wincor.ovv.viewer.dto.UserDto userDTO = new UserDto();
        userDTO.setId(id);
        userDTO.setUsername(username);
        userDTO.setFirstName(firstName);
        userDTO.setLastName(lastName);
        userDTO.setEmail(email);
        userDTO.setRoles(getRoles());
        userDTO.setActive(active);
        userDTO.setStores(stores);
        userDTO.setCountries(getCountries());
        return userDTO;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Arrays.asList(new SimpleGrantedAuthority("ROLE_" + role.name()));
    }

    @Override
    public boolean isAccountNonExpired() {
        return isAccountNonExpired;
    }

    public void setAccountNonExpired(boolean accountNonExpired) {
        isAccountNonExpired = accountNonExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return active;
    }

    @Override
    public boolean canAccessStore(Store store) {
        return getRole() == UserRole.ADMIN || getStores().contains(store);
    }

    @Override
    public Collection<UserRole> getRoles() {
        return List.of(getRole());
    }

    @Override
    public Collection<CountryEnum> getCountries() {
        if (countryCode == null) {
            return List.of(CountryEnum.values());
        } else {
            return List.of(countryCode);
        }
    }

    @Override
    public String getOvvRequestOperator() {
        return getUsername();
    }

    @Override
    public String getLoggerIdentifier() {
        return getUsername();
    }

    //~ Plain getters & setters

    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }

    public UserRole getRole() {
        return role;
    }
    public void setRole(UserRole role) {
        this.role = role;
    }

    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isActive() {
        return active;
    }
    public void setActive(boolean active) {
        this.active = active;
    }

    public Collection<Store> getStores() {
        return stores;
    }

    public void setStores(Collection<Store> stores) {
        this.stores = stores;
    }

    public void setCountryCode(CountryEnum countryCode) {
        this.countryCode = countryCode;
    }
}

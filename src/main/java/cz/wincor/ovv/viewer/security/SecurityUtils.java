package cz.wincor.ovv.viewer.security;

import cz.wincor.ovv.viewer.model.User;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.Store;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Security related utility methods.
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    /**
     * Find out if a user (represented by a {@link User} entity) is authenticated.
     * @return {@code true} if there is an authenticated user, {@code false} otherwise.
     */
    public static boolean isAuthenticated() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return auth != null && auth.isAuthenticated() && auth.getPrincipal() instanceof User;
    }

    /**
     * Get currently authenticated user.
     * @return {@link User} as the currently authenticated user. Never {@code null}.
     * @throws AccessDeniedException if no user is authenticated.
     */
    public static User getLoggedUser() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            throw new AccessDeniedException("Authenticated principal expected.");
        }
        Object principal = auth.getPrincipal();
        if (!(principal instanceof User)) {
            throw new AccessDeniedException("Principal '" + principal + "' expected to be a VM user.");
        }
        return (User) principal;
    }

    public static boolean hasRole(UserRole role) {
        return getLoggedUser().getRoles().contains(role);
    }

    public static void checkStoreAccess(Store store) {
        if (!getLoggedUser().canAccessStore(store)) {
            throw new AccessDeniedException("User is missing rights to store.");
        }
    }
}

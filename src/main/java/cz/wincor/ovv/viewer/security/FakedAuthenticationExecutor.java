package cz.wincor.ovv.viewer.security;

import cz.wincor.ovv.viewer.model.entity.OvvUser;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by jonas.ruzicka on 23.3.2018.
 */
public class FakedAuthenticationExecutor {
    public static <T> T execute(OvvUser user, ExecutionCallback<T> callback) {
        Authentication savedAuthentication = SecurityContextHolder.getContext().getAuthentication();
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole().name()));
        Authentication newAuth = new PreAuthenticatedAuthenticationToken(user, null, authorities);
        try {
            SecurityContextHolder.getContext().setAuthentication(newAuth);
            return callback.execute();
        } finally {
            SecurityContextHolder.getContext().setAuthentication(savedAuthentication);
        }
    }

    /**
     * Executor callback interface.
     * @param <T> Type of callback return value.
     */
    public interface ExecutionCallback<T> {
        T execute();
    }
}

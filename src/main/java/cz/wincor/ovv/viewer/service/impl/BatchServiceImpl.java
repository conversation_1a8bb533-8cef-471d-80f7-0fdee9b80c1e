package cz.wincor.ovv.viewer.service.impl;

import com.querydsl.core.BooleanBuilder;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.entity.QReportBatch;
import cz.wincor.ovv.viewer.model.entity.QReportFile;
import cz.wincor.ovv.viewer.model.entity.ReportBatch;
import cz.wincor.ovv.viewer.model.entity.ReportFile;
import cz.wincor.ovv.viewer.model.search.BatchSearchCriteria;
import cz.wincor.ovv.viewer.repository.ReportBatchRepository;
import cz.wincor.ovv.viewer.repository.ReportFileRepository;
import cz.wincor.ovv.viewer.service.BatchService;
import cz.wincor.ovv.viewer.xsd.CountryCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;

@Service
@Transactional(rollbackFor = ValidationException.class)
public class BatchServiceImpl extends BaseServiceImpl implements BatchService {
    private static final Logger LOG = LoggerFactory.getLogger(BatchServiceImpl.class);

    @Value("${app.batch.fileNameSearchSize}")
    private int fileNameSearchSize;

    @Autowired
    private ReportBatchRepository reportBatchRepository;

    @Autowired
    private ReportFileRepository reportFileRepository;

    @Override
    public Page<ReportBatch> getBatchFileDetail(BatchSearchCriteria searchCriteria) {
        Assert.notNull(searchCriteria, "Search criteria must be specified.");
        LOG.info("Getting batch file details : " + searchCriteria.toString());
        long start = System.currentTimeMillis();

        restrictSearchCriteria(searchCriteria);
        BooleanBuilder predicate = new BooleanBuilder();

        if (searchCriteria.getSentToIssuerFrom() != null) {
            predicate.and(QReportBatch.reportBatch.reportFile.created.goe(searchCriteria.getSentToIssuerFrom()));
        }
        if (searchCriteria.getSentToIssuerTo() != null) {
            predicate.and(QReportBatch.reportBatch.reportFile.created.lt(searchCriteria.getSentToIssuerTo()));
        }
        if (searchCriteria.getCountries() != null && !searchCriteria.getCountries().isEmpty()) {
            predicate.and(QReportBatch.reportBatch.reportFile.countryCode.in(searchCriteria.getCountries().stream().map(CountryCode::toString).toList()));
        }
        if (StringUtils.isNotBlank(searchCriteria.getVoucherNumber())) {
            predicate.and(QReportBatch.reportBatch.voucherNumber.eq(searchCriteria.getVoucherNumber()));
        }
        if (searchCriteria.getIssuer() != null && searchCriteria.getIssuer().length > 0) {
            predicate.and(QReportBatch.reportBatch.reportFile.ovvIssuer.in(searchCriteria.getIssuer()));
        }
        if (StringUtils.isNotBlank(searchCriteria.getFileName())) {
            predicate.and(QReportBatch.reportBatch.reportFile.fileName.likeIgnoreCase(searchCriteria.getFileName() + "%"));
        }
        if (searchCriteria.getReconciliated()) {
            predicate.and(QReportBatch.reportBatch.reportFile.reconciliated.isNotNull());
        }
        if (searchCriteria.getResultCode() != null) {
            predicate.and(QReportBatch.reportBatch.resultCode.eq(searchCriteria.getResultCode()));
        }
        if (searchCriteria.getStoreIds() != null && !searchCriteria.getStoreIds().isEmpty()) {
            predicate.and(QReportBatch.reportBatch.store.id.in(searchCriteria.getStoreIds()));
        }

        Page<ReportBatch> batches = findAll(reportBatchRepository, predicate, searchCriteria);
        long end = System.currentTimeMillis();
        LOG.info("Got batches in " + (end - start) + " ms.");
        return batches;
    }

    @Override
    public List<ReportFile> findByReportFileName(String text) {
        LOG.info("Finding report files");
        long start = System.currentTimeMillis();
        List<ReportFile> result = reportFileRepository.findAllByFileName(text + "%" , PageRequest.of(0, fileNameSearchSize));
        long end = System.currentTimeMillis();
        LOG.info("Found report files in " + (end - start) + " ms.");
        return result;
    }

    @Override
    public Page<ReportFile> getReportFile(BatchSearchCriteria searchCriteria) {
        Assert.notNull(searchCriteria, "Search criteria must be specified.");

        LOG.info("Getting report files : " + searchCriteria.toString());
        long start = System.currentTimeMillis();

        restrictSearchCriteria(searchCriteria);
        BooleanBuilder predicate = new BooleanBuilder();

        if (searchCriteria.getSentToIssuerFrom() != null) {
            predicate.and(QReportFile.reportFile.created.goe(searchCriteria.getSentToIssuerFrom()));
        }
        if (searchCriteria.getSentToIssuerTo() != null) {
            predicate.and(QReportFile.reportFile.created.lt(searchCriteria.getSentToIssuerTo()));
        }
        if (searchCriteria.getCountries() != null && !searchCriteria.getCountries().isEmpty()) {
            predicate.and(QReportFile.reportFile.countryCode.in(searchCriteria.getCountries().stream().map(CountryCode::toString).toList()));
        }
        if (searchCriteria.getIssuer() != null && searchCriteria.getIssuer().length > 0) {
            predicate.and(QReportFile.reportFile.ovvIssuer.in(searchCriteria.getIssuer()));
        }
        if (StringUtils.isNotBlank(searchCriteria.getFileName())) {
            predicate.and(QReportFile.reportFile.fileName.likeIgnoreCase(searchCriteria.getFileName() + "%"));
        }
        if (searchCriteria.getReconciliated()) {
            predicate.and(QReportFile.reportFile.reconciliated.isNotNull());
        }

        Page<ReportFile> result = findAll(reportFileRepository, predicate, searchCriteria);
        long end = System.currentTimeMillis();
        LOG.info("Got report files in " + (end - start) + " ms.");
        return result;
    }

}

package cz.wincor.ovv.viewer.service.impl;

import com.querydsl.core.BooleanBuilder;
import cz.wincor.ovv.viewer.dto.DtoPage;
import cz.wincor.ovv.viewer.dto.PasswordChangeRequest;
import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.OvvUser;
import cz.wincor.ovv.viewer.model.entity.QOvvUser;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.search.UserSearchCriteria;
import cz.wincor.ovv.viewer.repository.OvvUserRepository;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.security.FakedAuthenticationExecutor;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import cz.wincor.ovv.viewer.service.OvvUserService;
import cz.wincor.ovv.viewer.utils.MapperUtils;
import cz.wincor.ovv.viewer.validation.ErrorCode;
import cz.wincor.ovv.viewer.validation.ValidationHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.regex.Pattern;

/**
 * Default implementation of {@link OvvUserService}.
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = ValidationException.class)
public class OvvUserServiceImpl extends BaseServiceImpl implements OvvUserService {

    private static final Logger LOG = LoggerFactory.getLogger(OvvUserServiceImpl.class);

    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^.{8,}$");

    @Autowired
    private OvvUserRepository userRepository;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public UserDto getUserById(long userId) throws NotFoundException {
        return getCheckedUser(userId).mapToDto();
    }

    @Override
    public DtoPage<UserDto> getUsers(UserSearchCriteria searchCriteria) {
        LOG.info("Getting users : " + searchCriteria.toString());
        BooleanBuilder predicate = new BooleanBuilder();

        if (StringUtils.isNotBlank(searchCriteria.getUsername())) {
            predicate.and(QOvvUser.ovvUser.username.containsIgnoreCase(searchCriteria.getUsername()));
        }

        if (StringUtils.isNotBlank(searchCriteria.getName())) {
            predicate.and(QOvvUser.ovvUser.firstName.containsIgnoreCase(searchCriteria.getName())
                    .or(QOvvUser.ovvUser.lastName.containsIgnoreCase(searchCriteria.getName())));
        }

        if (searchCriteria.getRole() != null) {
            predicate.and(QOvvUser.ovvUser.role.eq(searchCriteria.getRole()));
        }

        if (searchCriteria.getActive() != null) {
            predicate.and(QOvvUser.ovvUser.active.eq(searchCriteria.getActive()));
        }

        OvvUser loggedUser = (OvvUser) SecurityUtils.getLoggedUser();
        // restrict stores for non-admin users and roles of users visible
        if (loggedUser.getRole() != UserRole.ADMIN) {
            BooleanBuilder storesCondition = new BooleanBuilder();
            for (Store store : loggedUser.getStores()) {
                storesCondition.or(QOvvUser.ovvUser.stores.contains(store));
            }
            predicate.and(storesCondition);
            predicate.and(QOvvUser.ovvUser.role.notIn(UserRole.ADMIN, UserRole.STORE_MANAGER));
        }

        return MapperUtils.mapPage(findAll(userRepository, predicate, searchCriteria));
    }

    @Override
    public UserDto createUser(UserDto userDto) throws ValidationException {
        LOG.info("Creating user : " + userDto.toString());
        // Validate user data
        validateUser(userDto, true, true);

        // Map all data to a new User entity
        OvvUser user = new OvvUser();
        user.setRole(userDto.getRoles().iterator().next());
        if (user.getRole().equals(UserRole.ADMIN)) {
            user.setCountryCode(null);
        } else {
            user.setCountryCode(userDto.getCountries().iterator().next());
        }
        user.setUsername(userDto.getUsername());
        user.setFirstName(userDto.getFirstName());
        user.setLastName(userDto.getLastName());
        user.setEmail(userDto.getEmail());
        user.setRole(userDto.getRoles().iterator().next());
        user.setPassword(passwordEncoder.encode(userDto.getPassword()));
        user.setAccountNonExpired(false);
        user.setActive(userDto.isActive());
        user.setStores(userDto.getStores());
        // Save the new User entity
        return userRepository.save(user).mapToDto();
    }

    @Override
    public UserDto updateUser(UserDto userDto) throws NotFoundException, ValidationException {
        LOG.info("Updating user : " + userDto.toString());
        // check whether the caller has filled in a new password
        boolean updatePassword = StringUtils.isNotBlank(userDto.getPassword());

        // Validate user data
        validateUser(userDto, false, updatePassword);

        OvvUser user = getCheckedUser(userDto.getId());
        user.setFirstName(userDto.getFirstName());
        user.setLastName(userDto.getLastName());
        if (updatePassword) {
            user.setPassword(passwordEncoder.encode(userDto.getPassword()));
            user.setAccountNonExpired(false);
        }
        user.setRole(userDto.getRoles().iterator().next());
        if (user.getRole().equals(UserRole.ADMIN)) {
            user.setCountryCode(null);
        } else {
            user.setCountryCode(userDto.getCountries().iterator().next());
        }
        user.setEmail(userDto.getEmail());
        user.setActive(userDto.isActive());
        user.setStores(userDto.getStores());
        // Save the new User entity
        return userRepository.save(user).mapToDto();
    }

    private void validateUser(UserDto userDto, boolean validateUsername, boolean validatePassword) throws ValidationException {
        ValidationHelper validation = new ValidationHelper();
        if (validateUsername) {
            validateUsername(validation, userDto.getUsername());
        }
        if (validatePassword) {
            validatePassword(validation, userDto.getPassword());
        }
        validation.notEmpty(userDto.getRoles(), "roles", "Missing user role.");
        validation.isTrue(userDto.getRoles().size() == 1, "roles", "roles.tooManyRoles", "Database user can have only a single role assigned.");
        validation.checkErrors();
        if(!userDto.getRoles().iterator().next().equals(UserRole.ADMIN)) {
            validation.notEmpty(userDto.getCountries(), "countries", "Missing user country.");
            validation.isTrue(userDto.getCountries().size() == 1, "countries", "countries.tooManyCountries", "Database user can have only a single country assigned.");
        }
        validation.checkErrors();
    }

    /**
     * Get {@link OvvUser} by its ID and verify that it exists.
     *
     * @param userId
     *            User ID.
     * @return Existing {@link OvvUser}. Never {@code null}.
     * @throws NotFoundException
     *             if user was not found.
     */
    private OvvUser getCheckedUser(long userId) throws NotFoundException {
        return userRepository.findById(userId).orElseThrow(()-> new NotFoundException("User", userId));
    }

    /**
     * Validate the the given username is not empty (blank string) and that it is
     * not used by an already existing user.
     *
     * @param validation
     *            Validation helper to process and collect validation errors.
     * @param username
     *            Username to be validated.
     */
    private void validateUsername(ValidationHelper validation, String username) {
        if (validation.notBlank(username, "username", "Username is missing.")) {
            OvvUser existingUser = userRepository.findByUsername(username);
            validation.isTrue(existingUser == null, "username", "username.taken", "Username is already taken.");
        }
    }

    /**
     * Validate new password. Passwords must meet these requirements for proper
     * password strength:
     * <ul>
     * <li>is at least 8 characters long,</li>
     * <li>contains at least 1 digit,</li>
     * <li>contains at least 1 lower case letter,</li>
     * <li>contains at least 1 upper case letter.</li>
     * </ul>
     *
     * @param validation
     *            Validation helper to process and collect validation errors.
     * @param password
     *            New password to be validated.
     */
    private void validatePassword(ValidationHelper validation, String password) {
        if (validation.notBlank(password, "password", "Password is missing")) {
            // validate password strength
            validation.isTrue(PASSWORD_PATTERN.matcher(password).matches(), "password", "password.tooWeak",
                    "Password is not strong enough.");
        }
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        OvvUser user = userRepository.findByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("User '" + username + "' was not found.");
        }
        return user;
    }

    @Override
    public void changePassword(long userId, PasswordChangeRequest passwordChangeRequest)
            throws ValidationException, NotFoundException {
        OvvUser user = getCheckedUser(userId);
        // Validate user data
        ValidationHelper validation = new ValidationHelper();

        if (!SecurityUtils.hasRole(UserRole.ADMIN)
                && !passwordEncoder.matches(passwordChangeRequest.getOldPassword(), user.getPassword())) {
            validation.error("oldPassword", ErrorCode.OBJECT_INVALID, "The old password does not match.");
        }
        validatePassword(validation, passwordChangeRequest.getNewPassword());
        validation.checkErrors();

        user.setPassword(passwordEncoder.encode(passwordChangeRequest.getNewPassword()));
        userRepository.save(user);

    }
    @Override
    public void expiredPasswordChange(cz.wincor.ovv.viewer.controller.api.PasswordChangeRequest passwordChangeRequest)
            throws ValidationException, NotFoundException {
        OvvUser user = userRepository.findByUsername(passwordChangeRequest.getUserName());
        // Validate user data
        ValidationHelper validation = new ValidationHelper();

        if (!passwordEncoder.matches(passwordChangeRequest.getOldPassword(), user.getPassword())) {
            validation.error("oldPassword", ErrorCode.OBJECT_INVALID, "The old password does not match.");
        }
        validatePassword(validation, passwordChangeRequest.getNewPassword());
        validation.checkErrors();

        user.setPassword(passwordEncoder.encode(passwordChangeRequest.getNewPassword()));
        user.setAccountNonExpired(true);
        FakedAuthenticationExecutor.execute(user, new FakedAuthenticationExecutor.ExecutionCallback<Void>() {
            @Override
            public Void execute() {
                userRepository.saveAndFlush(user);
                return null;
            }
        });
    }


    @Override
    public UserDto getLoggedUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetails principal = (UserDetails) authentication.getPrincipal();
        return null;
    }

}

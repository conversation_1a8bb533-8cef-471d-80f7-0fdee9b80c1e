package cz.wincor.ovv.viewer.service.impl;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.enums.StoreSearchScope;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.TescoOidcUser;
import cz.wincor.ovv.viewer.model.User;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.CashRegister;
import cz.wincor.ovv.viewer.model.entity.OvvUser;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.repository.CashRegisterRepository;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import cz.wincor.ovv.viewer.service.StoreService;
import cz.wincor.ovv.viewer.utils.CashRegisterNumberComparator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@Transactional(rollbackFor = ValidationException.class)
public class StoreServiceImpl extends BaseServiceImpl implements StoreService {

    private static final Logger LOG = LoggerFactory.getLogger(StoreServiceImpl.class);

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private CashRegisterRepository cashRegisterRepository;

    @Value("${app.store.searchSize}")
    private int storeSearchSize;

    @Override
    public List<Store> findStore(String store, CountryEnum country, StoreSearchScope searchScope) {
        LOG.info("Finding stores with search string : \'" + store + "\', search scope is : " + searchScope.name());
        User loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser instanceof TescoOidcUser tescoOidcUser) {
            return findStoresForOidcUser(tescoOidcUser, store, country);
        } else if (loggedUser instanceof OvvUser ovvUser) {
            return findStoresForDbUser(ovvUser, store, country, searchScope);
        } else {
            throw new IllegalArgumentException("Unsupported user instance: '" + loggedUser.getClass().getName() + "'.");
        }
    }

    @Override
    public List<CashRegister> findStoreCashRegisters(String siteCode, String number) {
        List<CashRegister> result = cashRegisterRepository.findAllBySiteCodeAndNumberFilter(siteCode, number);
        Collections.sort(result, new CashRegisterNumberComparator());
        return result;
    }

    private List<Store> findStoresForOidcUser(TescoOidcUser tescoOidcUser, String store, CountryEnum country) {
        if (tescoOidcUser.getRoles().contains(UserRole.ADMIN)) {
            return findAllStores(store, country);
        } else {
            return switch (tescoOidcUser.getBusinessCategory()) {
                case STORE -> storeRepository.findAllBySiteCode(tescoOidcUser.getDivision(), PageRequest.of(0, storeSearchSize));
                case OFFICE -> findStoresForOidcOfficeUser(tescoOidcUser, store, country);
            };
        }
    }

    private List<Store> findAllStores(String store, CountryEnum country) {
        if (country == null) {
            return storeRepository.findAllBySiteCodeOrName(
                    "%" + store + "%",
                    PageRequest.of(0, storeSearchSize)
            );
        } else {
            return storeRepository.findAllBySiteCodeOrNameAndCountry(
                    "%" + store + "%",
                    country,
                    PageRequest.of(0, storeSearchSize)
            );
        }
    }

    private List<Store> findStoresForOidcOfficeUser(TescoOidcUser tescoOidcUser, String store, CountryEnum country) {
        if (country == null) {
            return storeRepository.findAllBySiteCodeOrNameAndCountries(
                    "%" + store + "%",
                    tescoOidcUser.getCountries(),
                    PageRequest.of(0, storeSearchSize)
            );
        } else if (tescoOidcUser.getCountries().contains(country)) {
            return storeRepository.findAllBySiteCodeOrNameAndCountry(
                    "%" + store + "%",
                    country,
                    PageRequest.of(0, storeSearchSize)
            );
        } else {
            return Collections.emptyList();
        }
    }

    private List<Store> findStoresForDbUser(OvvUser ovvUser, String store, CountryEnum country, StoreSearchScope searchScope) {
        if (StoreSearchScope.USER.equals(searchScope) && !ovvUser.getRole().equals(UserRole.ADMIN)) {
            return storeRepository.findUserStoresBySiteCodeOrName(
                    ovvUser.getId(),
                    "%" + store + "%",
                    PageRequest.of(0, storeSearchSize)
            );
        } else {
            return findAllStores(store, country);
        }
    }
}

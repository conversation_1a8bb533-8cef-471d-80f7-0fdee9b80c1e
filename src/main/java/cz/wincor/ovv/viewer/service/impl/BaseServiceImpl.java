package cz.wincor.ovv.viewer.service.impl;

import com.querydsl.core.types.Predicate;
import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.TescoOidcUser;
import cz.wincor.ovv.viewer.model.User;
import cz.wincor.ovv.viewer.model.UserBusinessCategory;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.OvvUser;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.search.BaseSearchCriteria;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.security.access.AccessDeniedException;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Common ancestor of service interface implementations containing some convenient methods.
 *
 * <AUTHOR>
 */
public abstract class BaseServiceImpl {

    @Autowired
    private StoreRepository storeRepository;

    /**
     * Apply common restrictions based on user privileges to the given search criteria.
     * @param searchCriteria Search criteria which should be processed. Never {@code null}.
     */
    protected void restrictSearchCriteria(BaseSearchCriteria searchCriteria) {
        User loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser.getRoles().contains(UserRole.ADMIN)) {
            return;
        }
        if (loggedUser instanceof TescoOidcUser tescoOidcUser && tescoOidcUser.getBusinessCategory().equals(UserBusinessCategory.OFFICE)) {
            if ((searchCriteria.getSiteCodes() == null || searchCriteria.getSiteCodes().isEmpty()) &&
                    (searchCriteria.getStoreIds() == null || searchCriteria.getStoreIds().isEmpty())) {
                // optimizing query - restrict only by Office user's countries
                var userCountries = loggedUser.getCountries().stream().map(CountryEnum::asXsdCountryCode).toList();
                if (searchCriteria.getCountries() == null || searchCriteria.getCountries().isEmpty()) {
                    searchCriteria.setCountries(userCountries);
                } else {
                    var allowedCountries = searchCriteria.getCountries()
                            .stream()
                            .filter(userCountries::contains)
                            .toList();
                    if (allowedCountries.isEmpty()) {
                        throw new AccessDeniedException("User cannot access any requested countries: " + searchCriteria.getCountries());
                    }
                    searchCriteria.setCountries(allowedCountries);
                }
                return;
            }
        }
        Collection<Store> userStores;
        if (loggedUser instanceof TescoOidcUser tescoOidcUser) {
            userStores = switch (tescoOidcUser.getBusinessCategory()) {
                case STORE -> new ArrayList<>(storeRepository.findAllBySiteCode(tescoOidcUser.getDivision(), null));
                case OFFICE -> new ArrayList<>(storeRepository.findAllByCountries(loggedUser.getCountries(), null));
            };
        } else if (loggedUser instanceof OvvUser ovvUser) {
            userStores = ovvUser.getStores();
        } else {
            throw new IllegalArgumentException("Unsupported user instance: '" + loggedUser.getClass().getName() + "'.");
        }

        List<Long> userStoreIds = userStores.stream().map(Store::getId).toList();
        if (searchCriteria.getStoreIds() == null || searchCriteria.getStoreIds().isEmpty()) {
            searchCriteria.setStoreIds(userStoreIds);
        } else {
            List<Long> allowedStoreIds = searchCriteria.getStoreIds()
                    .stream()
                    .filter(userStoreIds::contains)
                    .toList();
            if (allowedStoreIds.isEmpty()) {
                throw new AccessDeniedException("User cannot access any requested stores: " + searchCriteria.getStoreIds());
            }
            searchCriteria.setStoreIds(allowedStoreIds);
        }

        List<String> userSiteCodes = userStores.stream().map(Store::getSiteCode).toList();
        if (searchCriteria.getSiteCodes() == null || searchCriteria.getSiteCodes().isEmpty()) {
            searchCriteria.setSiteCodes(userSiteCodes);
        } else {
            List<String> allowedSiteCodes = searchCriteria.getSiteCodes()
                    .stream()
                    .filter(userSiteCodes::contains)
                    .toList();
            if (allowedSiteCodes.isEmpty()) {
                throw new AccessDeniedException("User cannot access any requested sites: " + searchCriteria.getSiteCodes());
            }
            searchCriteria.setSiteCodes(allowedSiteCodes);
        }
    }

    /**
     * Create page request object for paging from the specified search criteria.
     * @param searchCriteria Search criteria object with common paging parameters. Never {@code null}.
     * @return {@link PageRequest} or {@code null} if paging parameters are not specified.
     */
    protected PageRequest createPageRequest(BaseSearchCriteria searchCriteria) {
        if (searchCriteria.getPage() == null || searchCriteria.getSize() == null) {
            return null;
        }
        return PageRequest.of(searchCriteria.getPage(), searchCriteria.getSize(), getSorting(searchCriteria));
    }

    /**
     * Get {@link Sort} from input parameters
     *
     * @param searchCriteria
     * @return
     */
    protected Sort getSorting(BaseSearchCriteria searchCriteria) {
        if (searchCriteria.getSortBy() == null || searchCriteria.getSortBy().isEmpty()) {
            return Sort.unsorted();
        }
        return Sort.by(searchCriteria.getDirection(), searchCriteria.getSortBy().toArray(new String[0]));
    }

    /**
     * Use repository to find all result with defined predicate and search criteria, creates correct page or sort requests
     * @param repository
     * @param predicate
     * @param searchCriteria
     * @return {@link Page} of results, may be empty, never null
     */
    protected <T> Page<T> findAll(QuerydslPredicateExecutor<T> repository, Predicate predicate, BaseSearchCriteria searchCriteria) {
       PageRequest pageRequest = createPageRequest(searchCriteria);
       if (pageRequest != null) {
           return repository.findAll(predicate, pageRequest);
       } else {
           return new PageImpl<T>(makeList(repository.findAll(predicate, getSorting(searchCriteria))));
       }
    }

    /**
     * Make list from {@link Iterable}
     * @param iter
     * @return
     */
    private static <E> List<E> makeList(Iterable<E> iter) {
        List<E> list = new ArrayList<E>();
        for (E item : iter) {
            list.add(item);
        }
        return list;
    }
}

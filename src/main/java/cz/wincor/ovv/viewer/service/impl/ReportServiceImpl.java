package cz.wincor.ovv.viewer.service.impl;

import com.querydsl.core.BooleanBuilder;
import cz.wincor.ovv.viewer.bootstrap.ConfigParams;
import cz.wincor.ovv.viewer.dto.DtoPage;
import cz.wincor.ovv.viewer.dto.ReportBasicDTO;
import cz.wincor.ovv.viewer.dto.ReportDTO;
import cz.wincor.ovv.viewer.dto.ReportParams;
import cz.wincor.ovv.viewer.dto.ReportSubmitRequest;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.ReportStatus;
import cz.wincor.ovv.viewer.model.ReportType;
import cz.wincor.ovv.viewer.model.TescoOidcUser;
import cz.wincor.ovv.viewer.model.User;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.OvvUser;
import cz.wincor.ovv.viewer.model.entity.QReport;
import cz.wincor.ovv.viewer.model.entity.Report;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.search.BaseSearchCriteria;
import cz.wincor.ovv.viewer.model.search.ReportSearchCriteria;
import cz.wincor.ovv.viewer.report.ReportParamsSerializer;
import cz.wincor.ovv.viewer.report.ReportProcessor;
import cz.wincor.ovv.viewer.repository.ReportRepository;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import cz.wincor.ovv.viewer.service.ReportService;
import cz.wincor.ovv.viewer.utils.MapperUtils;
import cz.wincor.ovv.viewer.validation.ErrorCode;
import cz.wincor.ovv.viewer.validation.ValidationHelper;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.DAYS;

/**
 * Implementation of reports
 *
 * <AUTHOR>
 */
@Service
public class ReportServiceImpl extends BaseServiceImpl implements ReportService {

    private static final Logger logger = LoggerFactory.getLogger(ReportServiceImpl.class);

    @Autowired
    private ReportRepository reportRepository;
    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private ReportProcessor reportProcessor;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private ConfigParams configParams;


    private ReportParamsSerializer reportParamsSerializer = new ReportParamsSerializer();


    @PostConstruct
    public void startReportProcessing() {
        List<Report> queuedReports = reportRepository.findByStatusOrderByCreatedDate(ReportStatus.QUEUED);
        queuedReports.stream().forEach(r -> reportProcessor.submit(r));
        new Thread(reportProcessor, "ReportProcessor").start();
    }

    @Override
    public ReportDTO submitReport(ReportSubmitRequest request) throws ValidationException {
        // Validate request
        ValidationHelper validation = new ValidationHelper();
        if (validation.notNull(request.getType(), "reportType", "Type of report is not specified.")) {
            validateReportParams(request.getType(), request.getParams(), validation);
        }
        validation.checkErrors();
        User loggedUser = SecurityUtils.getLoggedUser();
        Report report = transactionTemplate.execute(status -> {
            String params = null;
            if (request.getParams() != null) {
                params = reportParamsSerializer.serialize(request.getParams());
            }
            logger.info("Creating new report: type={}, params={}.", request.getType(), params);

            // Prepare new Report
            Report report1 = new Report();
            report1.setCreatedBy(loggedUser.getUsername());
            report1.setType(request.getType());
            report1.setStatus(ReportStatus.QUEUED);
            Long storeId = request.getParams().getStoreId();
            Store store = new Store();
            store.setId(storeId);
            report1.setStore(store);
            report1.setParams(params);

            // Save report
            report1 = reportRepository.save(report1);
            logger.info("New report saved: {}.", report1);
            return report1;

        });
        reportProcessor.submit(report);
        return report.mapToDto();
    }

    private void validateReportParams(ReportType type, ReportParams params, ValidationHelper validation) {
        if (!validation.notNull(params, "params", "Report parameters missing.")) {
            return;
        }

        // Validate reporting period
        switch (type) {
            case USED_VOUCHERS:
            case CATEGORY:
                if (validation.notNull(params.getFromDate(), "fromDate", "Start date is not specified.")
                        && validation.notNull(params.getToDate(), "toDate", "End date is not specified.")) {
                    if (params.getFromDate().isAfter(params.getToDate())) {
                        validation.error("dateInterval", ErrorCode.OBJECT_INVALID, "Reversed date interval.");
                    }
                    long daysBetween = DAYS.between(params.getFromDate(), params.getToDate());
                    if (daysBetween > configParams.getReportMaxDaysRange()) {
                        validation.error("dateInterval","tooBig" , "Interval is too big.");
                    }
                }
                break;
            default:
        }

        // Validate store ID
        Store store = null;
        if (params.getStoreId() != null) {
            store = storeRepository.findById(params.getStoreId()).orElse(null);
            validation.notNull(store, "storeId", "Invalid storeId value.");
        }
        SecurityUtils.checkStoreAccess(store);
    }

    @Override
    @Transactional(readOnly = true)
    public DtoPage<ReportBasicDTO> getReports(ReportSearchCriteria searchCriteria) {
        restrictSearchCriteria(searchCriteria);

        BooleanBuilder predicate = new BooleanBuilder();

        if (searchCriteria.getStoreIds() != null && !searchCriteria.getStoreIds().isEmpty()) {
            predicate.and(QReport.report.store.id.in(searchCriteria.getStoreIds()));
        }
        if (searchCriteria.getType() != null) {
            predicate.and(QReport.report.type.eq(searchCriteria.getType()));
        }
        if (searchCriteria.getStatus() != null) {
            predicate.and(QReport.report.status.eq(searchCriteria.getStatus()));
        }
        if (searchCriteria.getDateFrom() != null) {
            predicate.and(QReport.report.createdDate.goe(searchCriteria.getDateFrom().atStartOfDay()));
        }
        if (searchCriteria.getDateTo() != null) {
            predicate.and(QReport.report.createdDate.lt(searchCriteria.getDateTo().plusDays(1).atStartOfDay()));
        }

        return MapperUtils.mapPage(findAll(reportRepository, predicate, searchCriteria), this::mapReportToBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public ReportDTO getReportById(long reportId) throws NotFoundException {
        Report report = reportRepository.findById(reportId).orElse(null);
        if (report == null) {
            throw new NotFoundException("Report", reportId);
        }
        SecurityUtils.checkStoreAccess(report.getStore());
        return report.mapToDto();
    }

    private ReportBasicDTO mapReportToBasicDTO(Report report) {
        ReportBasicDTO dto = new ReportBasicDTO();
        dto.setId(report.getId());
        dto.setType(report.getType());
        dto.setStatus(report.getStatus());
        if (report.getStore() != null) {
            dto.setStore(report.getStore().mapToBasicDto());
        }
        dto.setCreatedDate(report.getCreatedDate());
        if (ReportStatus.OK == report.getStatus() || ReportStatus.FAILED == report.getStatus()) {
            dto.setFinishedDate(report.getModifiedDate());
        }
        if (report.getParams() != null && !report.getParams().isEmpty()) {
            ReportParams deserialized = reportParamsSerializer.deserialize(report.getParams());
            dto.setDataFrom(deserialized.getFromDate());
            dto.setDataTo(deserialized.getToDate());
        }
        return dto;
    }
}

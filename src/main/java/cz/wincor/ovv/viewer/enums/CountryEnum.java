package cz.wincor.ovv.viewer.enums;

import cz.wincor.ovv.viewer.xsd.CountryCode;

public enum CountryEnum {
    CZ, PL, HU, SK;

    public String getDivisionPrefix() {
        return switch (this) {
            case CZ -> "1";
            case SK -> "2";
            case PL -> "3";
            case HU -> "4";
        };
    }

    public CountryCode asXsdCountryCode() {
        return switch (this) {
            case CZ -> CountryCode.CZ;
            case PL -> CountryCode.PL;
            case HU -> CountryCode.HU;
            case SK -> CountryCode.SK;
        };
    }

    public static CountryEnum fromOidcClaimRole(String value) {
        return switch (value.toLowerCase()) {
            case "cz" -> CountryEnum.CZ;
            case "sk" -> CountryEnum.SK;
            case "pl" -> CountryEnum.PL;
            case "hu" -> CountryEnum.HU;
            default -> throw new IllegalArgumentException("Unknown country '" + value + "'.");
        };
    }
}

package cz.wincor.ovv.viewer.dto;

import java.time.LocalDateTime;

import cz.wincor.ovv.viewer.model.ReportStatus;
import cz.wincor.ovv.viewer.model.ReportType;

/**
 * DTO for Report entity.
 *
 * <AUTHOR>
 */
public class ReportDTO {

    private long id;
    private ReportType type;
    private ReportStatus status;
    private StoreDto store;
    private String params;
    private byte[] data;
    private LocalDateTime createdAt;
    private LocalDateTime finishedAt;
    private String createdBy;


    //~ Plain getters & setters

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public ReportType getType() {
        return type;
    }

    public void setType(ReportType type) {
        this.type = type;
    }

    public ReportStatus getStatus() {
        return status;
    }

    public void setStatus(ReportStatus status) {
        this.status = status;
    }

    public StoreDto getStore() {
        return store;
    }

    public void setStore(StoreDto store) {
        this.store = store;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getFinishedAt() {
        return finishedAt;
    }

    public void setFinishedAt(LocalDateTime finishedAt) {
        this.finishedAt = finishedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
}

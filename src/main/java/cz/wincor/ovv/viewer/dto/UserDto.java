package cz.wincor.ovv.viewer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.Store;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class UserDto {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;
    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private Collection<UserRole> roles;
    private boolean active;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String password;
    private Collection<Store> stores;
    private Collection<CountryEnum> countries;


    //~ Getters & setters

    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isActive() {
        return active;
    }
    public void setActive(boolean active) {
        this.active = active;
    }

    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }

    public Collection<UserRole> getRoles() {
        return roles;
    }
    public void setRoles(Collection<UserRole> roles) {
        this.roles = roles;
    }

    public Collection<Store> getStores() {
        return stores;
    }
    public void setStores(Collection<Store> stores) {
        this.stores = stores;
    }

    public Collection<CountryEnum> getCountries() {
        return countries;
    }
    public void setCountries(Collection<CountryEnum> countries) {
        this.countries = countries;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("UserDto [id=");
        builder.append(id);
        builder.append(", username=");
        builder.append(username);
        builder.append(", firstName=");
        builder.append(firstName);
        builder.append(", lastName=");
        builder.append(lastName);
        builder.append(", email=");
        builder.append(email);
        builder.append(", roles=");
        builder.append(roles);
        builder.append(", active=");
        builder.append(active);
        builder.append(", stores=");
        builder.append(stores);
        builder.append(", countries=");
        builder.append(countries);
        builder.append("]");
        return builder.toString();
    }
}

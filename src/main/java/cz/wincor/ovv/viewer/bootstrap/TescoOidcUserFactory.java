package cz.wincor.ovv.viewer.bootstrap;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.TescoOidcUser;
import cz.wincor.ovv.viewer.model.UserBusinessCategory;
import cz.wincor.ovv.viewer.model.UserRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings("unchecked")
public class TescoOidcUserFactory {

    private static final Logger logger = LoggerFactory.getLogger(TescoOidcUserFactory.class);

    public final static String PARAMS_CLAIM = "params";
    public final static String EMPLOYEE_NUMBER_PARAM = "EmployeeNumber";
    public final static String BUSINESS_CATEGORY_PARAM = "businessCategory";
    public final static String DIVISION_PARAM = "division";
    public final static String PREFERRED_USERNAME_CLAIM = "preferred_username";
    public final static String GROUPS_CLAIM = "groups";

    public OidcUser loadUserFromOidcClaims(OidcUser oidcUser) {
        Map<String, Object> claims = oidcUser.getClaims();
        Map<String, Object> employeeParams = (Map<String, Object>) claims.get(PARAMS_CLAIM);
        String employeeNumber = (String) employeeParams.get(EMPLOYEE_NUMBER_PARAM);
        String preferredUsername = (String) claims.get(PREFERRED_USERNAME_CLAIM);
        return loadUserFromOidcClaims(oidcUser, claims, employeeParams, employeeNumber, preferredUsername);
    }

    private OidcUser loadUserFromOidcClaims(
            OidcUser oidcUser,
            Map<String, Object> claims,
            Map<String, Object> employeeParams,
            String employeeNumber, String preferredUsername
    ) {
        List<String> groups = (List<String>) claims.get(GROUPS_CLAIM);
        if (groups == null || groups.isEmpty()) {
            throw new IllegalStateException("No roles provided.");
        }
        Set<UserRole> roles = new HashSet<>();
        Set<CountryEnum> countries = new HashSet<>();
        for (String group : groups) {
            GroupParseResult parsedGroup = parseGroup(group);
            if (parsedGroup != null) {
                roles.add(parsedGroup.getRole());
                countries.add(parsedGroup.getCountry());
            }
        }
        if (roles.isEmpty()) {
            String groupsString = String.join(", ", groups);
            throw new IllegalStateException("Unable to parse any role from provided groups '" + groupsString + "'.");
        } else if (roles.contains(UserRole.ADMIN)) {
            return new TescoOidcUser(oidcUser, employeeNumber, preferredUsername, roles, List.of(CountryEnum.values()), null, null);
        } else {
            return loadUserFromOidcClaims(oidcUser, employeeParams, employeeNumber, preferredUsername, roles, countries);
        }
    }

    private GroupParseResult parseGroup(String group) {
        try {
            // Tesco groups use following format: GG-CZ-TescoGlobal-OVV-Administrator
            String[] parts = group.split("-");
            UserRole role = UserRole.fromOidcClaimRole(parts[4]);
            CountryEnum country = CountryEnum.fromOidcClaimRole(parts[1]);
            return new GroupParseResult(role, country);
        } catch (Exception e) {
            logger.warn("Unable to parse group '" + group + "'. Ignoring.");
            return null;
        }
    }

    private OidcUser loadUserFromOidcClaims(
            OidcUser oidcUser,
            Map<String, Object> employeeParams,
            String employeeNumber,
            String preferredUsername,
            Collection<UserRole> roles,
            Collection<CountryEnum> countries
    ) {
        String businessCategoryString = (String) employeeParams.get(BUSINESS_CATEGORY_PARAM);
        UserBusinessCategory businessCategory = null;
        if (businessCategoryString != null) {
            businessCategory = UserBusinessCategory.fromOidcClaim(businessCategoryString);
        }
        if (businessCategory == UserBusinessCategory.STORE && countries.size() > 1) {
            throw new IllegalStateException("Store user with multiple countries assigned is not supported.");
        }
        String division = (String) employeeParams.get(DIVISION_PARAM);
        return new TescoOidcUser(oidcUser, employeeNumber, preferredUsername, roles, countries, businessCategory, division);
    }
}

class GroupParseResult {
    private final UserRole role;
    private final CountryEnum country;

    public GroupParseResult(UserRole role, CountryEnum country) {
        this.role = role;
        this.country = country;
    }

    public UserRole getRole() {
        return role;
    }

    public CountryEnum getCountry() {
        return country;
    }
}
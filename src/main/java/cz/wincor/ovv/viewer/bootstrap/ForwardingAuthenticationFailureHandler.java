package cz.wincor.ovv.viewer.bootstrap;


import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


public class ForwardingAuthenticationFailureHandler extends
        SimpleUrlAuthenticationFailureHandler {
    private final Map<String, FailureUrl> failureUrlMap = new HashMap<String, FailureUrl>();

    @Override
    public void onAuthenticationFailure(
            HttpServletRequest request,
            HttpServletResponse response,
            AuthenticationException exception
    ) throws IOException, ServletException {
        FailureUrl failureUrl = failureUrlMap.get(exception.getClass().getName());

        if (failureUrl != null && failureUrl.getUrl() != null) {
            // Redirect
            if (failureUrl.isRedirect()) {
                getRedirectStrategy().sendRedirect(request, response, failureUrl.getUrl());
            }
            // Forward
            else {
                request.getRequestDispatcher(failureUrl.getUrl()).forward(request, response);
            }
        } else {
            super.onAuthenticationFailure(request, response, exception);
        }
    }

    /**
     * Sets the map of exception types (by name) to URLs.
     *
     * @param failureUrlMap the map keyed by the fully-qualified name of the exception
     *                      class, with the corresponding failure URL as the value.
     * @throws IllegalArgumentException if the entries are not Strings or the URL is not
     *                                  valid.
     */
    public void setExceptionMappings(Map<?, FailureUrl> failureUrlMap) {
        this.failureUrlMap.clear();
        for (Map.Entry<?, FailureUrl> entry : failureUrlMap.entrySet()) {
            Object exception = entry.getKey();
            FailureUrl failureUrl = entry.getValue();
            Assert.isInstanceOf(String.class, exception,
                    "Exception key must be a String (the exception classname).");
            Assert.isInstanceOf(String.class, failureUrl.getUrl(), "URL must be a String");
            Assert.isTrue(UrlUtils.isValidRedirectUrl((String) failureUrl.getUrl()),
                    "Not a valid redirect URL: " + failureUrl);
            this.failureUrlMap.put((String) exception, failureUrl);
        }
    }

    public static class FailureUrl {
        private String url;
        private boolean redirect;

        public FailureUrl(String url, boolean redirect) {
            this.url = url;
            this.redirect = redirect;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public boolean isRedirect() {
            return redirect;
        }

        public void setRedirect(boolean redirect) {
            this.redirect = redirect;
        }
    }
}


package cz.wincor.ovv.viewer.bootstrap;

import cz.wincor.base.spring.security.CsrfCookieFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.registration.ClientRegistrations;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.AuthenticatedPrincipalOAuth2AuthorizedClientRepository;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizedClientRepository;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import java.util.HashMap;

/**
 * Spring Security configuration class.
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
public class SecurityConfig {

    @Autowired
    private ConfigParams configParams;

    // registration is also hardcoded in login-sso.html - login button
    private final String DEFAULT_OAUTH_REGISTRATION_ID = "default";

    private CsrfTokenRepository csrfTokenRepository = createCsrfTokenRepository();

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
    @Conditional(SsoEnabledCondition.class)
    public ClientRegistrationRepository clientRegistrationRepository() {
        ClientRegistration.Builder builder = ClientRegistrations
                        .fromIssuerLocation(configParams.getSsoOauthIssuer())
                        .registrationId(DEFAULT_OAUTH_REGISTRATION_ID)
                        .clientId(configParams.getSsoOauthClientId())
                        .clientSecret(configParams.getSsoOauthClientSecret())
                        .scope(configParams.getSsoOauthScope());
        if (configParams.getSsoOauthAuthEndpoint() != null && !configParams.getSsoOauthAuthEndpoint().isEmpty()) {
            builder.authorizationUri(configParams.getSsoOauthAuthEndpoint());
        }
        return new InMemoryClientRegistrationRepository(builder.build());
    }

    @Bean
    @Conditional(SsoEnabledCondition.class)
    public OAuth2AuthorizedClientService authorizedClientService(
            ClientRegistrationRepository clientRegistrationRepository) {
        return new InMemoryOAuth2AuthorizedClientService(clientRegistrationRepository);
    }

    @Bean
    @Conditional(SsoEnabledCondition.class)
    public OAuth2AuthorizedClientRepository authorizedClientRepository(
            OAuth2AuthorizedClientService authorizedClientService) {
        return new AuthenticatedPrincipalOAuth2AuthorizedClientRepository(authorizedClientService);
    }

    @Bean
    @Conditional(SsoEnabledCondition.class)
    public OidcUserService oidcUserService() {
        return new CustomOidcUserService(new TescoOidcUserFactory());
    }

    /**
     * Data API specific security configuration.
     */
    @Bean
    @Order(1)
    public SecurityFilterChain apiFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatcher("/api/**")
                .httpBasic(AbstractHttpConfigurer::disable)
                .logout(AbstractHttpConfigurer::disable)
                .anonymous(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(req -> req.anyRequest().authenticated())
                .csrf(csrf -> csrf.csrfTokenRepository(csrfTokenRepository))
                .headers(headers -> headers.cacheControl(Customizer.withDefaults()))
                .addFilterAfter(new CsrfCookieFilter(), CsrfFilter.class)
                .exceptionHandling(eh -> eh.authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)));
        return http.build();
    }


    /**
     * Security configuration for common web resources.
     */
    @Bean
    @Order(2)
    public SecurityFilterChain webResourcesFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/login**", "/error*", "/changePassword/**").permitAll()
                        .requestMatchers("/resources/**", "/app/**").permitAll()
                        .anyRequest().authenticated()
                )
                .logout(logout -> logout
                        .logoutRequestMatcher(new AntPathRequestMatcher("/logout", "GET"))
                        .logoutSuccessUrl("/login?logout=true")
                        .permitAll()
                        .invalidateHttpSession(true)
                )
                .httpBasic(AbstractHttpConfigurer::disable)
                .csrf(csrf -> csrf.csrfTokenRepository(csrfTokenRepository))
                .addFilterAfter(new CsrfCookieFilter(), CsrfFilter.class)
                .exceptionHandling(eh -> eh.accessDeniedPage("/error403"))
                .headers(headers -> headers.cacheControl(Customizer.withDefaults()));

        if (configParams.isSsoEnabled()) {
            http
                    .oauth2Login(oauth -> {
                        oauth.failureHandler(new SimpleUrlAuthenticationFailureHandler("/login?error=true"));
                    })
                    .formLogin(AbstractHttpConfigurer::disable);
        } else {
            http
                    .formLogin(formLogin -> {
                        formLogin.failureHandler(expiredPasswordAuthFailureHandler());
                        formLogin.loginPage("/login");
                        formLogin.usernameParameter("username");
                        formLogin.passwordParameter("password");
                        formLogin.loginProcessingUrl("/login");
                        formLogin.defaultSuccessUrl("/index.html");
                        formLogin.permitAll();
                    })
                    .oauth2Login(AbstractHttpConfigurer::disable);
        }

        return http.build();
    }

    /**
     * Create common CSRF token repository shared by both API and Web Resources.
     */
    private CsrfTokenRepository createCsrfTokenRepository() {
        HttpSessionCsrfTokenRepository repository = new HttpSessionCsrfTokenRepository();
        // Angular sends CSRF tokens in this HTTP header
        repository.setHeaderName("X-XSRF-TOKEN");
        return repository;
    }

    private static AuthenticationFailureHandler expiredPasswordAuthFailureHandler() {
        ForwardingAuthenticationFailureHandler handler = new ForwardingAuthenticationFailureHandler();
        HashMap<String, ForwardingAuthenticationFailureHandler.FailureUrl> failureUrlMap = new HashMap<>();

        ForwardingAuthenticationFailureHandler.FailureUrl failureUrl2 = new ForwardingAuthenticationFailureHandler.FailureUrl("/changePassword", false);
        failureUrlMap.put(AccountExpiredException.class.getName(), failureUrl2);
        handler.setExceptionMappings(failureUrlMap);
        handler.setDefaultFailureUrl("/login?error=true");
        return handler;
    }
}

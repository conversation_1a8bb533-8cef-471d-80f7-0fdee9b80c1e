package cz.wincor.ovv.viewer.bootstrap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

public class CustomOidcUserService extends OidcUserService {

    private static final Logger logger = LoggerFactory.getLogger(CustomOidcUserService.class);

    private final TescoOidcUserFactory oidcUserFactory;

    public CustomOidcUserService(TescoOidcUserFactory oidcUserFactory) {
        this.oidcUserFactory = oidcUserFactory;
    }

    @Override
    public OidcUser loadUser(OidcUserRequest userRequest) throws OAuth2AuthenticationException {
        OidcUser oidcUser = super.loadUser(userRequest);
        try {
            return oidcUserFactory.loadUserFromOidcClaims(oidcUser);
        } catch (Exception e) {
            logger.error("Unable to login via SSO.", e);
            throw new OidcMappingException("Unable to login via SSO. " + e.getMessage(), e);
        }
    }
}

class OidcMappingException extends AuthenticationException {

    public OidcMappingException(String msg, Throwable cause) {
        super(msg, cause);
    }
}
package cz.wincor.ovv.viewer.controller.api;

import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.service.OvvUserService;
import cz.wincor.ovv.viewer.validation.ValidationError;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Collection;

/**
 * Created by jonas.ruzicka on 22.3.2018.
 */
@Controller
@RequestMapping("/changePassword")
public class ChangePasswordController {

    @Autowired
    OvvUserService ovvUserService;

    @RequestMapping(method = RequestMethod.POST)
    public String renderLogin(PasswordChangeForm passwordChangeForm, Model model, HttpServletRequest request) {
        model.addAttribute("userName", request.getParameter("username"));
        return "changePassword";
    }

    @RequestMapping(path = "/process", method = RequestMethod.POST)
    public String changePassword(@RequestParam("userName") String userName, @RequestParam("userId") String userId,
                                 @ModelAttribute PasswordChangeForm passwordChangeForm, Model model, BindingResult result,
                                 HttpServletRequest request, RedirectAttributes ra) throws ServletException {

        PasswordChangeFormValidator validator = new PasswordChangeFormValidator();
        validator.validate(passwordChangeForm, result);
        if (!result.hasErrors()) {

            PasswordChangeRequest passwordChangeRequest = new PasswordChangeRequest();
            passwordChangeRequest.setOldPassword(passwordChangeForm.getOldPassword());
            passwordChangeRequest.setNewPassword(passwordChangeForm.getNewPassword());
            passwordChangeRequest.setUserName(userName);
            try {
                ovvUserService.expiredPasswordChange(passwordChangeRequest);
            } catch (ValidationException e) {
                Collection<ValidationError> errors = e.getErrors();
                for (ValidationError error : errors) {
                    result.reject(error.getCode());
                }

                model.addAttribute("userName", userName);
                model.addAttribute("userId", userId);
                return "changePassword";
            }
            catch (NotFoundException e) {
                result.reject(e.getLocalizedMessage());
            }
            request.logout();
            ra.addAttribute("passwordChanged", true);
            return "redirect:/login";
        } else {
            model.addAttribute("userName", userName);
            model.addAttribute("userId", userId);
            return "changePassword";
        }
    }
}

package cz.wincor.ovv.viewer.controller;

import cz.wincor.ovv.viewer.bootstrap.ConfigParams;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDate;

/**
 * MVC interceptor adding common attributes to model for rendering.
 *
 * <AUTHOR>
 */
public class CommonModelAttrsInterceptor implements AsyncHandlerInterceptor {

    private ConfigParams configParams;

    public CommonModelAttrsInterceptor(ConfigParams configParams) {
        this.configParams = configParams;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            ModelAndView mav) throws Exception {
        // Current year (displayed in footer)
        mav.getModel().put("currentYear", LocalDate.now().getYear());
        mav.getModel().put("appVersion", configParams.getAppVersion());
        mav.getModel().put("ssoEnabled", configParams.isSsoEnabled());
        // Currently logged in user
        if (SecurityUtils.isAuthenticated()) {
            mav.getModel().put("loggedUsername", SecurityUtils.getLoggedUser().getUsername());
        }
    }
}

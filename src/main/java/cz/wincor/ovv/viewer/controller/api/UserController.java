package cz.wincor.ovv.viewer.controller.api;

import cz.wincor.ovv.viewer.controller.api.vo.DrawRequest;
import cz.wincor.ovv.viewer.controller.api.vo.DrawResult;
import cz.wincor.ovv.viewer.dto.DtoPage;
import cz.wincor.ovv.viewer.dto.PasswordChangeRequest;
import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.User;
import cz.wincor.ovv.viewer.model.entity.OvvUser;
import cz.wincor.ovv.viewer.model.search.UserSearchCriteria;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import cz.wincor.ovv.viewer.service.OvvUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private OvvUserService userService;


    @GetMapping
    public DrawResult<UserDto> getUser(UserSearchCriteria searchCriteria, DrawRequest drawRequest) {
        checkOvvUser();
        drawRequest.mapToSearchCriteria(searchCriteria);
        DtoPage<UserDto> page = userService.getUsers(searchCriteria);
        return new DrawResult<>(drawRequest, page);
    }

    @GetMapping(path = "/{userId}")
    public UserDto getUser(@PathVariable("userId") long userId) throws NotFoundException {
        checkOvvUser();
        return userService.getUserById(userId);
    }

    @PostMapping
    public void createUser(@RequestBody UserDto user) throws ValidationException {
        checkOvvUser();
        userService.createUser(user);
    }

    @PutMapping(path = "/{userId}")
    public void updateUser(@PathVariable("userId") int userId, @RequestBody UserDto user) throws NotFoundException, ValidationException {
        checkOvvUser();
        userService.updateUser(user);
    }

    @RequestMapping(value = "/{userId}/changeStatus", method = RequestMethod.POST)
    public void changeUserStatus(@PathVariable("userId") int userId, boolean active) throws NotFoundException, ValidationException {
        checkOvvUser();
        UserDto user = userService.getUserById(userId);
        if (user.isActive() != active) {
            user.setActive(active);
            userService.updateUser(user);
        }
    }

    @RequestMapping(value = "/{userId}/changePassword", method = RequestMethod.POST)
    public void changePassword(@PathVariable("userId") int userId, @RequestBody PasswordChangeRequest request) throws ValidationException, NotFoundException {
        checkOvvUser();
        userService.changePassword(userId, request);
    }

    @GetMapping(value = "/getLoggedUser")
    public UserDto getLoggedUser() {
        return SecurityUtils.getLoggedUser().mapToDto();
    }

    private void checkOvvUser() {
        User loggedUser = SecurityUtils.getLoggedUser();
        if (!(loggedUser instanceof OvvUser)) {
            throw new AccessDeniedException("Non OvvUser cannot access user management API.");
        }
    }
}

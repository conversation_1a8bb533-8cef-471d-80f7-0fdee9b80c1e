package cz.wincor.ovv.viewer;

import cz.wincor.ovv.viewer.bootstrap.AppConfig;
import cz.wincor.ovv.viewer.bootstrap.DataSourceConfig;
import cz.wincor.ovv.viewer.bootstrap.JpaConfig;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.web.WebAppConfiguration;

@ExtendWith(SpringExtension.class)
@WebAppConfiguration
@ContextConfiguration(classes = {
        AppConfig.class,
        DataSourceConfig.class,
        JpaConfig.class,
        TestContextConfiguration.class})
public abstract class BaseSpringTest {
}

class TestContextConfiguration {

    @Bean
    @Primary
    public ClientRegistrationRepository clientRegistrationRepository() {
        return registrationId -> null;
    }
}

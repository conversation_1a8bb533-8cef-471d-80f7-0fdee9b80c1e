package cz.wincor.ovv.viewer.bootstrap;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.TescoOidcUser;
import cz.wincor.ovv.viewer.model.UserBusinessCategory;
import cz.wincor.ovv.viewer.model.UserRole;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.function.Executable;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CustomTescoOidcUserFactoryTest {

    private TescoOidcUserFactory oidcUserFactory;

    @BeforeEach
    public void setup() {
        oidcUserFactory = new TescoOidcUserFactory();
    }

    @Test
    public void mapUserFromOidcClaimsHappyCase() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(Collections.singletonList("GG-CZ-TescoGlobal-OVV-Viewer"))
                .withEmployeeNumber("123")
                .withBusinessCategory("Store")
                .withDivision("12345")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        OidcUser result = oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertInstanceOf(TescoOidcUser.class, result);
        TescoOidcUser resultCasted = (TescoOidcUser) result;
        assertEquals("<EMAIL>", resultCasted.getUsername());
        assertEquals("123", resultCasted.getEmployeeNumber());
        assertEquals(UserRole.VIEWER, resultCasted.getRoles().iterator().next());
        assertEquals(CountryEnum.CZ, resultCasted.getCountries().iterator().next());
        assertEquals(UserBusinessCategory.STORE, resultCasted.getBusinessCategory());
        assertEquals("12345", resultCasted.getDivision());
    }

    @Test
    public void adminHasNoCountryNorBusinessCategoryNorDivision() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(Collections.singletonList("GG-CZ-TescoGlobal-OVV-Administrator"))
                .withEmployeeNumber("123")
                .withBusinessCategory("Store")
                .withDivision("12345")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        OidcUser result = oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertInstanceOf(TescoOidcUser.class, result);
        TescoOidcUser resultCasted = (TescoOidcUser) result;
        assertEquals(UserRole.ADMIN, resultCasted.getRoles().iterator().next());
        assertEquals(List.of(CountryEnum.values()), resultCasted.getCountries());
        assertNull(resultCasted.getBusinessCategory());
        assertNull(resultCasted.getDivision());
    }

    @Test
    public void businessCategoryAndDivisionIsNotMandatoryForAdmin() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(Collections.singletonList("GG-CZ-TescoGlobal-OVV-Administrator"))
                .withEmployeeNumber("123")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        OidcUser result = oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertInstanceOf(TescoOidcUser.class, result);
        TescoOidcUser resultCasted = (TescoOidcUser) result;
        assertEquals(UserRole.ADMIN, resultCasted.getRoles().iterator().next());
        assertNull(resultCasted.getBusinessCategory());
        assertNull(resultCasted.getDivision());
    }

    @Test
    public void adminCanHaveRolesAcrossMultipleCountries() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(Arrays.asList("GG-CZ-TescoGlobal-OVV-Administrator", "GG-SK-TescoGlobal-OVV-Administrator"))
                .withEmployeeNumber("123")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        OidcUser result = oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertInstanceOf(TescoOidcUser.class, result);
        TescoOidcUser resultCasted = (TescoOidcUser) result;
        assertEquals(UserRole.ADMIN, resultCasted.getRoles().iterator().next());
        assertEquals(List.of(CountryEnum.values()), resultCasted.getCountries());
    }

    @Test
    public void officeUserCanHaveMultipleCountries() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(Arrays.asList("GG-CZ-TescoGlobal-OVV-Viewer", "GG-SK-TescoGlobal-OVV-Viewer"))
                .withEmployeeNumber("123")
                .withBusinessCategory("Office")
                .withDivision("12345")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        OidcUser result = oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertInstanceOf(TescoOidcUser.class, result);
        TescoOidcUser resultCasted = (TescoOidcUser) result;
        assertEquals(UserRole.VIEWER, resultCasted.getRoles().iterator().next());
        assertEquals(2, resultCasted.getCountries().size());
        List<CountryEnum> expected = List.of(CountryEnum.CZ, CountryEnum.SK);
        assertTrue(resultCasted.getCountries().containsAll(expected));
        assertTrue(expected.containsAll(resultCasted.getCountries()));
    }

    @Test
    public void officeUserHasNoDivision() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(List.of("GG-CZ-TescoGlobal-OVV-Viewer"))
                .withEmployeeNumber("123")
                .withBusinessCategory("Office")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        OidcUser result = oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertInstanceOf(TescoOidcUser.class, result);
        TescoOidcUser resultCasted = (TescoOidcUser) result;
        assertNull(resultCasted.getDivision());
    }

    @Test
    public void storeUserCannotHaveRolesAcrossMultipleCountries() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(Arrays.asList("GG-CZ-TescoGlobal-OVV-Viewer", "GG-SK-TescoGlobal-OVV-Viewer"))
                .withEmployeeNumber("123")
                .withBusinessCategory("Store")
                .withDivision("12345")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        Executable callback = () -> oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertThrows(IllegalStateException.class, callback);
    }

    @Test
    public void claimsMustContainAtLeastOneRole() {
        // given
        Map<String, Object> claims = new ClaimsBuilder()
                .withPreferredUsername("<EMAIL>")
                .withGroups(Collections.emptyList())
                .withEmployeeNumber("123")
                .withBusinessCategory("Store")
                .withDivision("12345")
                .build();
        OidcUser oidcUser = anOidcUser(claims);

        // when
        Executable callback = () -> oidcUserFactory.loadUserFromOidcClaims(oidcUser);

        // then
        assertThrows(IllegalStateException.class, callback);
    }

    private OidcUser anOidcUser(Map<String, Object> claims) {
        OidcUser oidcUser = mock(OidcUser.class);
        when(oidcUser.getClaims()).thenReturn(claims);
        return oidcUser;
    }
}

class ClaimsBuilder {

    private String preferredUsername;
    private Collection<String> groups;
    private String employeeNumber;
    private String businessCategory;
    private String division;

    public ClaimsBuilder withPreferredUsername(String preferredUsername) {
        this.preferredUsername = preferredUsername;
        return this;
    }

    public ClaimsBuilder withGroups(Collection<String> groups) {
        this.groups = groups;
        return this;
    }

    public ClaimsBuilder withEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
        return this;
    }

    public ClaimsBuilder withBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
        return this;
    }

    public ClaimsBuilder withDivision(String division) {
        this.division = division;
        return this;
    }

    public Map<String, Object> build() {
        return new HashMap<String, Object>() {{
            put(TescoOidcUserFactory.PREFERRED_USERNAME_CLAIM, preferredUsername);
            put(TescoOidcUserFactory.GROUPS_CLAIM, groups);
            put(TescoOidcUserFactory.PARAMS_CLAIM, new HashMap<String, Object>() {{
                put(TescoOidcUserFactory.EMPLOYEE_NUMBER_PARAM, employeeNumber);
                put(TescoOidcUserFactory.BUSINESS_CATEGORY_PARAM, businessCategory);
                put(TescoOidcUserFactory.DIVISION_PARAM, division);
            }});
        }};
    }
}
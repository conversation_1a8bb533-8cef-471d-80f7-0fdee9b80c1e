## How to run Database locally in Docker

It is possible to use docker-compose to run database locally.
1. Go to directory `tools/postgre-in-docker-compose`
2. use command `docker-compose up`
3. edit values in`src/main/resources/jdbc.properties`
```properties
jdbc.driver = org.postgresql.Driver
jdbc.url = ******************************************
jdbc.username = ovv
jdbc.password = pass
jpa.database.platform = org.hibernate.dialect.PostgreSQL91Dialect
```
